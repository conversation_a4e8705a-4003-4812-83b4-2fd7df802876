#!/usr/bin/env python3
"""
系统问题修复脚本
自动检测和修复常见问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
os.chdir(PROJECT_ROOT)

def clear_cache():
    """清理Python缓存"""
    print("🧹 清理Python缓存...")
    
    cache_dirs = []
    
    # 查找所有__pycache__目录
    for cache_dir in PROJECT_ROOT.rglob("__pycache__"):
        cache_dirs.append(cache_dir)
    
    # 查找所有.pyc文件
    pyc_files = list(PROJECT_ROOT.rglob("*.pyc"))
    
    # 删除缓存目录
    for cache_dir in cache_dirs:
        try:
            shutil.rmtree(cache_dir)
            print(f"✅ 删除缓存目录: {cache_dir}")
        except Exception as e:
            print(f"⚠️ 无法删除 {cache_dir}: {e}")
    
    # 删除.pyc文件
    for pyc_file in pyc_files:
        try:
            pyc_file.unlink()
            print(f"✅ 删除缓存文件: {pyc_file}")
        except Exception as e:
            print(f"⚠️ 无法删除 {pyc_file}: {e}")
    
    print(f"🎉 清理完成: {len(cache_dirs)} 个目录, {len(pyc_files)} 个文件")

def install_missing_packages():
    """安装缺失的包"""
    print("📦 检查并安装缺失的包...")
    
    packages = [
        "streamlit",
        "pandas", 
        "numpy",
        "requests",
        "plotly",
        "psutil",
        "fastapi",
        "uvicorn"
    ]
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"⚠️ {package} 缺失，正在安装...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package} 安装失败: {e}")

def fix_streamlit_config():
    """修复Streamlit配置"""
    print("🔧 修复Streamlit配置...")
    
    # 创建Streamlit配置目录
    streamlit_dir = Path.home() / ".streamlit"
    streamlit_dir.mkdir(exist_ok=True)
    
    # 创建配置文件
    config_file = streamlit_dir / "config.toml"
    
    config_content = """
[server]
headless = true
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false

[theme]
base = "light"
"""
    
    try:
        with open(config_file, 'w') as f:
            f.write(config_content)
        print(f"✅ Streamlit配置已创建: {config_file}")
    except Exception as e:
        print(f"⚠️ 无法创建Streamlit配置: {e}")

def setup_directories():
    """设置目录结构"""
    print("📁 设置目录结构...")
    
    directories = [
        'data',
        'data/scenarios',
        'data/uploads', 
        'data/backups',
        'logs',
        'models',
        'static'
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 目录: {directory}")

def check_file_permissions():
    """检查文件权限"""
    print("🔐 检查文件权限...")
    
    important_files = [
        "start.py",
        "start_simple.py", 
        "frontend/main.py",
        "install_dependencies.py"
    ]
    
    for file_path in important_files:
        file_obj = Path(file_path)
        if file_obj.exists():
            try:
                # 尝试读取文件
                with open(file_obj, 'r') as f:
                    f.read(1)
                print(f"✅ 文件可读: {file_path}")
            except Exception as e:
                print(f"❌ 文件权限问题: {file_path} - {e}")
        else:
            print(f"⚠️ 文件不存在: {file_path}")

def test_imports():
    """测试关键模块导入"""
    print("🧪 测试模块导入...")
    
    test_modules = [
        ("streamlit", "Streamlit前端框架"),
        ("pandas", "数据处理"),
        ("numpy", "数值计算"),
        ("requests", "HTTP客户端"),
        ("plotly", "数据可视化"),
        ("psutil", "系统监控"),
        ("fastapi", "后端API框架"),
        ("uvicorn", "ASGI服务器")
    ]
    
    success_count = 0
    
    for module, description in test_modules:
        try:
            __import__(module)
            print(f"✅ {module} ({description})")
            success_count += 1
        except ImportError:
            print(f"❌ {module} ({description}) - 导入失败")
    
    print(f"\n📊 导入测试结果: {success_count}/{len(test_modules)} 成功")
    
    if success_count >= 5:
        print("✅ 基础模块可用，系统可以运行")
        return True
    else:
        print("❌ 关键模块缺失，系统可能无法正常运行")
        return False

def clear_streamlit_cache():
    """清理Streamlit缓存"""
    print("🧹 清理Streamlit缓存...")

    try:
        # 清理Streamlit缓存目录
        streamlit_cache_dir = Path.home() / ".streamlit"
        if streamlit_cache_dir.exists():
            cache_files = list(streamlit_cache_dir.rglob("*.cache"))
            for cache_file in cache_files:
                try:
                    cache_file.unlink()
                    print(f"✅ 删除缓存: {cache_file}")
                except Exception as e:
                    print(f"⚠️ 无法删除 {cache_file}: {e}")

        print("✅ Streamlit缓存清理完成")
    except Exception as e:
        print(f"⚠️ 清理Streamlit缓存失败: {e}")

def restart_streamlit():
    """重启Streamlit进程"""
    print("🔄 检查Streamlit进程...")

    try:
        # 查找Streamlit进程
        result = subprocess.run(
            ["tasklist", "/FI", "IMAGENAME eq python.exe"],
            capture_output=True, text=True, shell=True
        )

        if "streamlit" in result.stdout.lower():
            print("⚠️ 发现运行中的Streamlit进程")
            print("💡 建议:")
            print("   1. 刷新浏览器页面")
            print("   2. 或重新启动系统")
        else:
            print("✅ 没有发现冲突的Streamlit进程")

    except Exception as e:
        print(f"⚠️ 无法检查进程: {e}")

def main():
    """主函数"""
    print("🔧 LLM任务分配系统 - 问题修复脚本")
    print("=" * 50)
    
    print("\n🚀 开始系统修复...")
    
    # 1. 清理缓存
    clear_cache()

    # 2. 清理Streamlit缓存
    clear_streamlit_cache()

    # 3. 设置目录
    setup_directories()

    # 4. 修复Streamlit配置
    fix_streamlit_config()

    # 5. 检查文件权限
    check_file_permissions()

    # 6. 安装缺失包
    install_missing_packages()

    # 7. 测试导入
    imports_ok = test_imports()

    # 8. 检查Streamlit进程
    restart_streamlit()
    
    print("\n🎉 修复完成!")
    
    if imports_ok:
        print("\n✅ 系统状态良好，可以启动:")
        print("   python start_simple.py  # 简化启动")
        print("   python start.py         # 完整启动")
    else:
        print("\n⚠️ 仍有问题需要解决:")
        print("   1. 手动安装缺失的包")
        print("   2. 检查Python环境")
        print("   3. 重启命令行/IDE")
    
    print("\n💡 如果问题仍然存在:")
    print("   1. 重启浏览器")
    print("   2. 清理浏览器缓存")
    print("   3. 使用新的浏览器标签页")

if __name__ == "__main__":
    main()
