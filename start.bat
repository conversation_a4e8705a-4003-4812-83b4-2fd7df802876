@echo off
echo 🚁 LLM任务分配系统启动脚本
echo ================================

echo 📦 安装依赖包...
pip install fastapi uvicorn streamlit pandas requests plotly

echo.
echo 🔧 创建后端目录...
if not exist "backend" mkdir backend
if not exist "data" mkdir data
if not exist "logs" mkdir logs

echo.
echo 🚀 启动后端服务...
start "后端服务" cmd /k "python -m uvicorn backend.simple_app:app --host 0.0.0.0 --port 8000"

echo.
echo ⏳ 等待后端启动...
timeout /t 5 /nobreak

echo.
echo 🚀 启动前端服务...
start "前端服务" cmd /k "streamlit run frontend/main.py --server.port 8501"

echo.
echo 🎉 系统启动完成!
echo 📍 前端地址: http://localhost:8501
echo 📍 后端API: http://localhost:8000
echo 📍 API文档: http://localhost:8000/docs
echo 🔐 默认账号: admin / admin123
echo.
echo 按任意键退出...
pause
