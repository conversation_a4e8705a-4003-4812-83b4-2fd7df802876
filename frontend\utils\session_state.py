import streamlit as st
from typing import Any, Dict


def init_session_state():
    """初始化会话状态"""
    
    # 认证相关
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    
    if 'access_token' not in st.session_state:
        st.session_state.access_token = None
    
    if 'user_info' not in st.session_state:
        st.session_state.user_info = None
    
    # 导航相关
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'dashboard'
    
    if 'previous_page' not in st.session_state:
        st.session_state.previous_page = None
    
    # 数据缓存
    if 'scenarios_cache' not in st.session_state:
        st.session_state.scenarios_cache = []
    
    if 'tasks_cache' not in st.session_state:
        st.session_state.tasks_cache = []
    
    if 'models_cache' not in st.session_state:
        st.session_state.models_cache = []
    
    if 'users_cache' not in st.session_state:
        st.session_state.users_cache = []
    
    # 缓存时间戳
    if 'cache_timestamps' not in st.session_state:
        st.session_state.cache_timestamps = {}
    
    # UI状态
    if 'show_create_scenario' not in st.session_state:
        st.session_state.show_create_scenario = False
    
    if 'show_create_task' not in st.session_state:
        st.session_state.show_create_task = False
    
    if 'show_create_model' not in st.session_state:
        st.session_state.show_create_model = False
    
    if 'selected_scenario_id' not in st.session_state:
        st.session_state.selected_scenario_id = None
    
    if 'selected_task_id' not in st.session_state:
        st.session_state.selected_task_id = None
    
    if 'selected_model_id' not in st.session_state:
        st.session_state.selected_model_id = None
    
    # 表单状态
    if 'form_data' not in st.session_state:
        st.session_state.form_data = {}
    
    # 错误和消息
    if 'error_messages' not in st.session_state:
        st.session_state.error_messages = []
    
    if 'success_messages' not in st.session_state:
        st.session_state.success_messages = []
    
    # 监控数据
    if 'monitoring_data' not in st.session_state:
        st.session_state.monitoring_data = {}
    
    # 实时数据
    if 'realtime_updates' not in st.session_state:
        st.session_state.realtime_updates = True
    
    # 用户偏好
    if 'user_preferences' not in st.session_state:
        st.session_state.user_preferences = {
            'theme': 'light',
            'language': 'zh-CN',
            'auto_refresh': True,
            'refresh_interval': 30,
            'notifications_enabled': True
        }


def set_page(page_name: str):
    """设置当前页面"""
    st.session_state.previous_page = st.session_state.current_page
    st.session_state.current_page = page_name


def get_current_page() -> str:
    """获取当前页面"""
    return st.session_state.get('current_page', 'dashboard')


def get_previous_page() -> str:
    """获取上一个页面"""
    return st.session_state.get('previous_page', 'dashboard')


def set_cache(key: str, data: Any, ttl: int = 300):
    """设置缓存数据"""
    import time
    st.session_state[f"{key}_cache"] = data
    st.session_state.cache_timestamps[key] = time.time() + ttl


def get_cache(key: str) -> Any:
    """获取缓存数据"""
    import time
    cache_key = f"{key}_cache"
    
    if cache_key not in st.session_state:
        return None
    
    # 检查缓存是否过期
    if key in st.session_state.cache_timestamps:
        if time.time() > st.session_state.cache_timestamps[key]:
            # 缓存过期，清除数据
            del st.session_state[cache_key]
            del st.session_state.cache_timestamps[key]
            return None
    
    return st.session_state[cache_key]


def clear_cache(key: str = None):
    """清除缓存数据"""
    if key:
        cache_key = f"{key}_cache"
        if cache_key in st.session_state:
            del st.session_state[cache_key]
        if key in st.session_state.cache_timestamps:
            del st.session_state.cache_timestamps[key]
    else:
        # 清除所有缓存
        cache_keys = [k for k in st.session_state.keys() if k.endswith('_cache')]
        for cache_key in cache_keys:
            del st.session_state[cache_key]
        st.session_state.cache_timestamps = {}


def add_error_message(message: str):
    """添加错误消息"""
    st.session_state.error_messages.append(message)


def add_success_message(message: str):
    """添加成功消息"""
    st.session_state.success_messages.append(message)


def get_error_messages() -> list:
    """获取错误消息"""
    messages = st.session_state.error_messages.copy()
    st.session_state.error_messages = []  # 清空消息
    return messages


def get_success_messages() -> list:
    """获取成功消息"""
    messages = st.session_state.success_messages.copy()
    st.session_state.success_messages = []  # 清空消息
    return messages


def set_form_data(form_name: str, data: Dict[str, Any]):
    """设置表单数据"""
    st.session_state.form_data[form_name] = data


def get_form_data(form_name: str) -> Dict[str, Any]:
    """获取表单数据"""
    return st.session_state.form_data.get(form_name, {})


def clear_form_data(form_name: str = None):
    """清除表单数据"""
    if form_name:
        if form_name in st.session_state.form_data:
            del st.session_state.form_data[form_name]
    else:
        st.session_state.form_data = {}


def set_user_preference(key: str, value: Any):
    """设置用户偏好"""
    st.session_state.user_preferences[key] = value


def get_user_preference(key: str, default: Any = None) -> Any:
    """获取用户偏好"""
    return st.session_state.user_preferences.get(key, default)


def reset_session_state():
    """重置会话状态"""
    # 保留认证信息
    auth_keys = ['authenticated', 'access_token', 'user_info']
    auth_data = {key: st.session_state.get(key) for key in auth_keys}
    
    # 清除所有状态
    for key in list(st.session_state.keys()):
        del st.session_state[key]
    
    # 重新初始化
    init_session_state()
    
    # 恢复认证信息
    for key, value in auth_data.items():
        if value is not None:
            st.session_state[key] = value


def is_authenticated() -> bool:
    """检查是否已认证"""
    return st.session_state.get('authenticated', False)


def get_current_user() -> Dict[str, Any]:
    """获取当前用户信息"""
    return st.session_state.get('user_info', {})


def update_monitoring_data(data: Dict[str, Any]):
    """更新监控数据"""
    st.session_state.monitoring_data.update(data)


def get_monitoring_data() -> Dict[str, Any]:
    """获取监控数据"""
    return st.session_state.monitoring_data
