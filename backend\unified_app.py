
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON>NResponse
from datetime import datetime
import json
from pathlib import Path

app = FastAPI(
    title="LLM任务分配系统API",
    description="无人机干扰决策系统后端API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "LLM任务分配系统API",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "backend",
        "timestamp": datetime.now().isoformat(),
        "uptime": "running"
    }

@app.get("/api/v1/health")
async def api_health():
    return {
        "status": "healthy",
        "version": "1.0.0",
        "api": "v1",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/status")
async def system_status():
    return {
        "backend": "online",
        "database": "sqlite",
        "llm": "not_configured",
        "frontend": "connected",
        "timestamp": datetime.now().isoformat()
    }

# 模拟用户认证
@app.post("/api/v1/auth/login")
async def login(credentials: dict = None):
    return {
        "access_token": "demo_token_" + datetime.now().strftime("%Y%m%d%H%M%S"),
        "token_type": "bearer",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "role": "admin"
        }
    }

@app.get("/api/v1/users/me")
async def get_current_user():
    return {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "role": "admin",
        "created_at": "2024-01-01T00:00:00",
        "last_login": datetime.now().isoformat()
    }

@app.get("/api/v1/scenarios")
async def get_scenarios():
    return {
        "scenarios": [
            {
                "id": 1,
                "name": "演示场景1",
                "description": "基础演示场景",
                "map_size": "10x10",
                "drone_count": 5,
                "created_at": "2024-01-01T00:00:00"
            },
            {
                "id": 2,
                "name": "演示场景2", 
                "description": "高级演示场景",
                "map_size": "20x20",
                "drone_count": 10,
                "created_at": "2024-01-01T00:00:00"
            }
        ],
        "total": 2
    }

@app.post("/api/v1/scenarios")
async def create_scenario(scenario: dict):
    return {
        "id": 3,
        "name": scenario.get("name", "新场景"),
        "description": scenario.get("description", ""),
        "created_at": datetime.now().isoformat(),
        "status": "created"
    }

@app.get("/api/v1/models")
async def get_models():
    return {
        "models": [
            {
                "id": 1,
                "name": "本地模型",
                "type": "local",
                "status": "available"
            },
            {
                "id": 2,
                "name": "远程模型",
                "type": "remote", 
                "status": "configured"
            }
        ],
        "total": 2
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
