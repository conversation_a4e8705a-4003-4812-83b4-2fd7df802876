import streamlit as st
import sqlite3
import hashlib
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from frontend.utils.logger import log_operation, log_system
from frontend.pages.security_page import record_login_attempt, record_security_event


def init_database():
    """初始化用户数据库"""
    db_path = Path("data/users.db")
    db_path.parent.mkdir(exist_ok=True)

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 创建用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            email TEXT,
            role TEXT DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # 创建默认管理员账户
    admin_password = hashlib.sha256("admin123".encode()).hexdigest()
    cursor.execute('''
        INSERT OR IGNORE INTO users (username, password_hash, email, role)
        VALUES (?, ?, ?, ?)
    ''', ("admin", admin_password, "<EMAIL>", "admin"))

    conn.commit()
    conn.close()


def verify_user(username, password):
    """验证用户登录"""
    db_path = Path("data/users.db")
    if not db_path.exists():
        return None

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    password_hash = hashlib.sha256(password.encode()).hexdigest()
    cursor.execute('''
        SELECT id, username, email, role FROM users
        WHERE username = ? AND password_hash = ?
    ''', (username, password_hash))

    user = cursor.fetchone()
    conn.close()

    if user:
        return {
            'id': user[0],
            'username': user[1],
            'email': user[2],
            'role': user[3]
        }
    return None


def register_user(username, password, email):
    """注册新用户"""
    db_path = Path("data/users.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cursor.execute('''
            INSERT INTO users (username, password_hash, email, role)
            VALUES (?, ?, ?, ?)
        ''', (username, password_hash, email, "user"))

        conn.commit()
        conn.close()
        return True
    except sqlite3.IntegrityError:
        conn.close()
        return False


def show():
    """显示登录页面"""
    # 初始化数据库
    init_database()

    # 系统标题
    st.markdown("""
    <div style="text-align: center; margin-bottom: 3rem;">
        <h1 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
            🚁 无人机干扰决策系统
        </h1>
        <p style="color: #64748b;">基于大语言模型的智能轨迹规划与干扰决策平台</p>
    </div>
    """, unsafe_allow_html=True)

    # 简化的登录/注册/忘记密码切换
    if 'show_register' not in st.session_state:
        st.session_state.show_register = False
    if 'show_forgot_password' not in st.session_state:
        st.session_state.show_forgot_password = False

    if st.session_state.show_forgot_password:
        show_forgot_password_form()
        if st.button("← 返回登录", key="back_to_login_from_forgot"):
            st.session_state.show_forgot_password = False
            st.rerun()
    elif st.session_state.show_register:
        show_register_form()
        if st.button("← 返回登录", key="back_to_login"):
            st.session_state.show_register = False
            st.rerun()
    else:
        show_login_form()
        col1, col2 = st.columns(2)
        with col1:
            if st.button("📝 没有账号？点击注册", key="go_to_register"):
                st.session_state.show_register = True
                st.rerun()
        with col2:
            if st.button("🔑 忘记密码？", key="go_to_forgot"):
                st.session_state.show_forgot_password = True
                st.rerun()

    # 演示账号信息
    st.info("💡 演示账号: admin / admin123")


def show_login_form():
    """显示登录表单"""
    st.markdown("### 🔑 用户登录")

    with st.form("login_form"):
        username = st.text_input("👤 用户名", placeholder="请输入用户名")
        password = st.text_input("🔒 密码", type="password", placeholder="请输入密码")

        if st.form_submit_button("🚀 登录", type="primary", use_container_width=True):
            if username and password:
                user = verify_user(username, password)
                if user:
                    st.session_state.authenticated = True
                    st.session_state.user = user
                    st.session_state.current_page = 'model_config'  # 登录后跳转到模型配置

                    # 记录登录日志和安全审计
                    log_operation("用户登录", "认证", {"username": username}, "success")
                    record_login_attempt(username, "127.0.0.1", "Streamlit", True)

                    st.success("✅ 登录成功！正在跳转...")
                    st.rerun()
                else:
                    # 记录登录失败日志和安全审计
                    log_operation("用户登录失败", "认证", {"username": username}, "error")
                    record_login_attempt(username, "127.0.0.1", "Streamlit", False)

                    # 检查是否需要记录安全事件
                    # 这里可以添加更复杂的逻辑，比如检测暴力破解
                    record_security_event(
                        "登录失败",
                        "MEDIUM",
                        f"用户 {username} 登录失败",
                        ip_address="127.0.0.1",
                        details={"username": username}
                    )

                    st.error("❌ 用户名或密码错误")
            else:
                st.error("⚠️ 请输入用户名和密码")


def show_register_form():
    """显示注册表单"""
    st.markdown("### 📝 用户注册")

    with st.form("register_form"):
        username = st.text_input("👤 用户名", placeholder="请输入用户名（3-20个字符）")
        email = st.text_input("📧 邮箱", placeholder="请输入邮箱地址")
        password = st.text_input("🔒 密码", type="password", placeholder="请输入密码（至少6位）")
        confirm_password = st.text_input("🔒 确认密码", type="password", placeholder="请再次输入密码")

        agree_terms = st.checkbox("我同意用户协议和隐私政策")

        if st.form_submit_button("📝 注册", type="primary", use_container_width=True):
            if not all([username, email, password, confirm_password]):
                st.error("⚠️ 请填写所有必填项")
            elif len(username) < 3 or len(username) > 20:
                st.error("⚠️ 用户名长度应为3-20个字符")
            elif len(password) < 6:
                st.error("⚠️ 密码长度至少6位")
            elif password != confirm_password:
                st.error("⚠️ 两次输入的密码不一致")
            elif not agree_terms:
                st.error("⚠️ 请同意用户协议")
            else:
                if register_user(username, password, email):
                    # 记录注册成功日志
                    log_operation("用户注册", "认证", {"username": username, "email": email}, "success")
                    st.success("✅ 注册成功！请使用新账号登录")
                else:
                    # 记录注册失败日志
                    log_operation("用户注册失败", "认证", {"username": username, "email": email}, "error")
                    st.error("❌ 注册失败，用户名可能已存在")


def show_forgot_password_form():
    """显示忘记密码表单"""
    st.markdown("### 🔑 重置密码")

    with st.form("forgot_password_form"):
        username = st.text_input("👤 用户名", placeholder="请输入您的用户名")
        email = st.text_input("📧 邮箱", placeholder="请输入注册时的邮箱地址")
        new_password = st.text_input("🔒 新密码", type="password", placeholder="请输入新密码（至少6位）")
        confirm_new_password = st.text_input("🔒 确认新密码", type="password", placeholder="请再次输入新密码")

        if st.form_submit_button("🔄 重置密码", type="primary", use_container_width=True):
            if not all([username, email, new_password, confirm_new_password]):
                st.error("⚠️ 请填写所有必填项")
            elif len(new_password) < 6:
                st.error("⚠️ 新密码长度至少6位")
            elif new_password != confirm_new_password:
                st.error("⚠️ 两次输入的新密码不一致")
            else:
                if reset_password(username, email, new_password):
                    st.success("✅ 密码重置成功！请使用新密码登录")
                else:
                    st.error("❌ 密码重置失败，请检查用户名和邮箱是否正确")


def reset_password(username, email, new_password):
    """重置用户密码"""
    db_path = Path("data/users.db")
    if not db_path.exists():
        return False

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 验证用户名和邮箱是否匹配
        cursor.execute('''
            SELECT id FROM users WHERE username = ? AND email = ?
        ''', (username, email))

        user = cursor.fetchone()
        if not user:
            conn.close()
            return False

        # 更新密码
        new_password_hash = hashlib.sha256(new_password.encode()).hexdigest()
        cursor.execute('''
            UPDATE users SET password_hash = ? WHERE username = ? AND email = ?
        ''', (new_password_hash, username, email))

        conn.commit()
        conn.close()
        return True

    except Exception:
        conn.close()
        return False
