#!/usr/bin/env python3
"""
LLM任务分配系统简化启动脚本
在缺少某些依赖的情况下也能启动基础功能
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
os.chdir(PROJECT_ROOT)

# 添加项目路径到Python路径
sys.path.insert(0, str(PROJECT_ROOT))

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    required_packages = {
        'streamlit': '前端框架',
        'pandas': '数据处理',
        'numpy': '数值计算',
        'requests': 'HTTP客户端',
        'plotly': '数据可视化'
    }
    
    optional_packages = {
        'fastapi': '后端API框架',
        'uvicorn': 'ASGI服务器',
        'psutil': '系统监控',
        'passlib': '密码加密',
        'sqlmodel': '数据库ORM'
    }
    
    missing_required = []
    missing_optional = []
    
    # 检查必需包
    for package, description in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} ({description})")
        except ImportError:
            print(f"❌ {package} ({description}) - 必需")
            missing_required.append(package)
    
    # 检查可选包
    for package, description in optional_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} ({description})")
        except ImportError:
            print(f"⚠️ {package} ({description}) - 可选")
            missing_optional.append(package)
    
    if missing_required:
        print(f"\n❌ 缺少必需依赖: {', '.join(missing_required)}")
        print("💡 请运行: python install_dependencies.py")
        return False
    
    if missing_optional:
        print(f"\n⚠️ 缺少可选依赖: {', '.join(missing_optional)}")
        print("💡 某些高级功能可能不可用")
    
    print("✅ 基础依赖检查通过")
    return True

def setup_directories():
    """设置目录结构"""
    print("📁 设置目录结构...")
    
    directories = [
        'data',
        'logs',
        'models',
        'static',
        'data/uploads',
        'data/scenarios',
        'data/backups'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构设置完成")

def start_frontend_only():
    """仅启动前端"""
    print("🚀 启动前端服务...")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONPATH': str(PROJECT_ROOT),
        'STREAMLIT_SERVER_HEADLESS': 'true'
    })
    
    # 启动Streamlit服务
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        "frontend/main.py",
        "--server.port", "8501",
        "--server.address", "0.0.0.0"
    ]
    
    try:
        print("📍 前端地址: http://localhost:8501")
        print("💡 提示: 后端服务未启动，某些功能可能不可用")
        print("🛑 按 Ctrl+C 停止服务")
        
        subprocess.run(cmd, env=env, cwd=PROJECT_ROOT)
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def start_backend():
    """启动后端服务"""
    try:
        import fastapi
        import uvicorn
    except ImportError:
        print("⚠️ 后端依赖不可用，跳过后端启动")
        return None
    
    print("🚀 启动后端服务...")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONPATH': str(PROJECT_ROOT),
        'DATABASE_URL': 'sqlite:///./data/app.db',
        'SECRET_KEY': 'dev-secret-key-change-in-production',
        'ENVIRONMENT': 'development',
        'DEBUG': 'True'
    })
    
    # 启动FastAPI服务
    cmd = [
        sys.executable, "-m", "uvicorn",
        "backend.app.main:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ]
    
    try:
        process = subprocess.Popen(
            cmd,
            env=env,
            cwd=PROJECT_ROOT,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        print("✅ 后端服务已启动")
        return process
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def start_full_system():
    """启动完整系统"""
    print("🚀 启动完整系统...")
    
    # 启动后端
    backend_process = start_backend()
    if backend_process:
        time.sleep(3)  # 等待后端启动
    
    # 启动前端
    print("🚀 启动前端服务...")
    
    env = os.environ.copy()
    env.update({
        'PYTHONPATH': str(PROJECT_ROOT),
        'BACKEND_URL': 'http://localhost:8000'
    })
    
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        "frontend/main.py",
        "--server.port", "8501",
        "--server.address", "0.0.0.0"
    ]
    
    try:
        print("\n🎉 系统启动完成!")
        if backend_process:
            print("📍 前端地址: http://localhost:8501")
            print("📍 后端API: http://localhost:8000")
            print("📍 API文档: http://localhost:8000/docs")
        else:
            print("📍 前端地址: http://localhost:8501")
            print("⚠️ 后端服务未启动，某些功能可能不可用")
        
        print("🛑 按 Ctrl+C 停止系统")
        
        subprocess.run(cmd, env=env, cwd=PROJECT_ROOT)
        
    except KeyboardInterrupt:
        print("\n🛑 正在停止系统...")
        if backend_process:
            backend_process.terminate()
            backend_process.wait()
        print("✅ 系统已停止")
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        if backend_process:
            backend_process.terminate()

def main():
    """主函数"""
    print("🚁 LLM任务分配系统 - 简化启动脚本")
    print("=" * 50)
    
    # 设置目录
    setup_directories()
    
    # 检查依赖
    if not check_dependencies():
        print("\n💡 建议:")
        print("   1. 运行 python install_dependencies.py 安装依赖")
        print("   2. 或者手动安装: pip install streamlit pandas numpy requests plotly")
        
        response = input("\n❓ 是否仍要尝试启动？(y/n): ").lower().strip()
        if response != 'y':
            sys.exit(1)
    
    # 询问启动模式
    print("\n🔧 选择启动模式:")
    print("   1. 完整系统 (前端 + 后端)")
    print("   2. 仅前端 (基础功能)")
    
    try:
        choice = input("\n请选择 (1/2): ").strip()
        
        if choice == "1":
            start_full_system()
        elif choice == "2":
            start_frontend_only()
        else:
            print("❌ 无效选择")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 启动已取消")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
