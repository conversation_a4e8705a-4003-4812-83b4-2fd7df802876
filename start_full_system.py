#!/usr/bin/env python3
"""
LLM任务分配系统 - 统一启动脚本
同时启动前端和后端服务
"""

import os
import sys
import subprocess
import time
import threading
import signal
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
os.chdir(PROJECT_ROOT)

# 添加项目路径到Python路径
sys.path.insert(0, str(PROJECT_ROOT))

# 全局进程列表
processes = []

def signal_handler(sig, frame):
    """信号处理器，用于优雅关闭"""
    print("\n🛑 正在关闭系统...")
    for process in processes:
        if process and process.poll() is None:
            process.terminate()
    
    # 等待进程结束
    time.sleep(2)
    
    # 强制结束
    for process in processes:
        if process and process.poll() is None:
            process.kill()
    
    print("✅ 系统已关闭")
    sys.exit(0)

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    required_packages = {
        'streamlit': '前端框架',
        'fastapi': '后端框架',
        'uvicorn': 'ASGI服务器',
        'pandas': '数据处理',
        'requests': 'HTTP客户端'
    }
    
    missing_packages = []
    
    for package, description in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} ({description})")
        except ImportError:
            print(f"❌ {package} ({description}) - 缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖: {', '.join(missing_packages)}")
        print("💡 安装命令:")
        for package in missing_packages:
            print(f"   pip install {package}")
        return False
    
    print("✅ 依赖检查通过")
    return True

def setup_directories():
    """设置目录结构"""
    print("📁 设置目录结构...")
    
    directories = [
        'data',
        'data/scenarios',
        'data/uploads',
        'data/backups',
        'logs',
        'backend',
        'static'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构设置完成")

def create_simple_backend():
    """创建简化后端"""
    print("🔧 创建后端应用...")
    
    backend_content = '''
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from datetime import datetime
import json
from pathlib import Path

app = FastAPI(
    title="LLM任务分配系统API",
    description="无人机干扰决策系统后端API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "LLM任务分配系统API",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "backend",
        "timestamp": datetime.now().isoformat(),
        "uptime": "running"
    }

@app.get("/api/v1/health")
async def api_health():
    return {
        "status": "healthy",
        "version": "1.0.0",
        "api": "v1",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/status")
async def system_status():
    return {
        "backend": "online",
        "database": "sqlite",
        "llm": "not_configured",
        "frontend": "connected",
        "timestamp": datetime.now().isoformat()
    }

# 模拟用户认证
@app.post("/api/v1/auth/login")
async def login(credentials: dict = None):
    return {
        "access_token": "demo_token_" + datetime.now().strftime("%Y%m%d%H%M%S"),
        "token_type": "bearer",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "role": "admin"
        }
    }

@app.get("/api/v1/users/me")
async def get_current_user():
    return {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "role": "admin",
        "created_at": "2024-01-01T00:00:00",
        "last_login": datetime.now().isoformat()
    }

@app.get("/api/v1/scenarios")
async def get_scenarios():
    return {
        "scenarios": [
            {
                "id": 1,
                "name": "演示场景1",
                "description": "基础演示场景",
                "map_size": "10x10",
                "drone_count": 5,
                "created_at": "2024-01-01T00:00:00"
            },
            {
                "id": 2,
                "name": "演示场景2", 
                "description": "高级演示场景",
                "map_size": "20x20",
                "drone_count": 10,
                "created_at": "2024-01-01T00:00:00"
            }
        ],
        "total": 2
    }

@app.post("/api/v1/scenarios")
async def create_scenario(scenario: dict):
    return {
        "id": 3,
        "name": scenario.get("name", "新场景"),
        "description": scenario.get("description", ""),
        "created_at": datetime.now().isoformat(),
        "status": "created"
    }

@app.get("/api/v1/models")
async def get_models():
    return {
        "models": [
            {
                "id": 1,
                "name": "本地模型",
                "type": "local",
                "status": "available"
            },
            {
                "id": 2,
                "name": "远程模型",
                "type": "remote", 
                "status": "configured"
            }
        ],
        "total": 2
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    backend_dir = Path("backend")
    backend_dir.mkdir(exist_ok=True)
    
    backend_file = backend_dir / "unified_app.py"
    with open(backend_file, 'w', encoding='utf-8') as f:
        f.write(backend_content)
    
    print(f"✅ 后端应用已创建: {backend_file}")
    return backend_file

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONPATH': str(PROJECT_ROOT),
        'ENVIRONMENT': 'development'
    })
    
    # 启动命令
    cmd = [
        sys.executable, "-m", "uvicorn",
        "backend.unified_app:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ]
    
    try:
        process = subprocess.Popen(
            cmd,
            env=env,
            cwd=PROJECT_ROOT,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        processes.append(process)
        print("✅ 后端服务已启动 (PID: {})".format(process.pid))
        return process
        
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONPATH': str(PROJECT_ROOT),
        'STREAMLIT_SERVER_HEADLESS': 'true',
        'STREAMLIT_BROWSER_GATHER_USAGE_STATS': 'false'
    })
    
    # 启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        "frontend/main.py",
        "--server.port", "8501",
        "--server.address", "0.0.0.0",
        "--server.headless", "true"
    ]
    
    try:
        process = subprocess.Popen(
            cmd,
            env=env,
            cwd=PROJECT_ROOT,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        processes.append(process)
        print("✅ 前端服务已启动 (PID: {})".format(process.pid))
        return process
        
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        return None

def monitor_processes():
    """监控进程状态"""
    while True:
        time.sleep(10)
        
        for i, process in enumerate(processes):
            if process and process.poll() is not None:
                print(f"⚠️ 进程 {i+1} 已退出 (返回码: {process.returncode})")
                
                # 读取错误输出
                if process.stdout:
                    output = process.stdout.read()
                    if output:
                        print(f"输出: {output}")

def main():
    """主函数"""
    print("🚁 LLM任务分配系统 - 统一启动")
    print("=" * 50)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("\n🚀 开始启动系统...")
    
    # 1. 检查依赖
    if not check_dependencies():
        print("\n💡 建议运行: python fix_all_issues.py")
        response = input("\n❓ 是否仍要尝试启动？(y/n): ").lower().strip()
        if response != 'y':
            sys.exit(1)
    
    # 2. 设置目录
    setup_directories()
    
    # 3. 创建后端
    create_simple_backend()
    
    # 4. 启动后端
    backend_process = start_backend()
    if not backend_process:
        print("❌ 后端启动失败，退出")
        sys.exit(1)
    
    # 等待后端启动
    print("⏳ 等待后端服务启动...")
    time.sleep(5)
    
    # 5. 启动前端
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ 前端启动失败，但后端仍在运行")
    
    # 等待前端启动
    print("⏳ 等待前端服务启动...")
    time.sleep(3)
    
    # 6. 显示启动信息
    print("\n🎉 系统启动完成!")
    print("=" * 50)
    print("📍 前端地址: http://localhost:8501")
    print("📍 后端API: http://localhost:8000")
    print("📍 API文档: http://localhost:8000/docs")
    print("📍 健康检查: http://localhost:8000/health")
    print("=" * 50)
    print("🔐 默认账号: admin / admin123")
    print("🛑 按 Ctrl+C 停止系统")
    print("=" * 50)
    
    # 7. 启动监控线程
    monitor_thread = threading.Thread(target=monitor_processes, daemon=True)
    monitor_thread.start()
    
    # 8. 保持主线程运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)

if __name__ == "__main__":
    main()
