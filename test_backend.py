#!/usr/bin/env python3
"""
测试后端连接脚本
"""

import requests
import time
import subprocess
import sys
from pathlib import Path

def test_connection():
    """测试后端连接"""
    print("🔍 测试后端连接...")
    
    endpoints = [
        ("根路径", "http://localhost:8000/"),
        ("健康检查", "http://localhost:8000/health"),
        ("API健康检查", "http://localhost:8000/api/v1/health")
    ]
    
    all_good = True
    
    for name, url in endpoints:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: 连接成功")
                data = response.json()
                print(f"   响应: {data}")
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
                all_good = False
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: 连接失败 - 后端服务未启动")
            all_good = False
        except Exception as e:
            print(f"❌ {name}: 错误 - {e}")
            all_good = False
        print()
    
    return all_good

def start_backend_test():
    """启动后端进行测试"""
    print("🚀 启动后端进行测试...")
    
    # 检查后端文件
    backend_file = Path("backend/simple_app.py")
    if not backend_file.exists():
        print("❌ 后端文件不存在")
        return False
    
    try:
        # 启动后端
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn",
            "backend.simple_app:app",
            "--host", "0.0.0.0",
            "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("⏳ 等待后端启动...")
        
        # 等待启动
        for i in range(30):
            try:
                response = requests.get("http://localhost:8000/health", timeout=2)
                if response.status_code == 200:
                    print("✅ 后端启动成功!")
                    return process
            except:
                pass
            time.sleep(1)
            print(f"   等待中... ({i+1}/30)")
        
        print("❌ 后端启动超时")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def main():
    """主函数"""
    print("🧪 后端连接测试")
    print("=" * 30)
    
    # 首先测试是否已有后端在运行
    if test_connection():
        print("🎉 后端服务正常运行!")
        return
    
    print("⚠️ 后端服务未运行，尝试启动...")
    print()
    
    # 启动后端进行测试
    process = start_backend_test()
    
    if process:
        print()
        print("🔍 重新测试连接...")
        if test_connection():
            print("🎉 后端测试成功!")
        else:
            print("❌ 后端测试失败")
        
        print("\n🛑 停止测试后端...")
        process.terminate()
        time.sleep(2)
        if process.poll() is None:
            process.kill()
        print("✅ 测试完成")
    else:
        print("❌ 无法启动后端进行测试")
    
    print("\n💡 解决方案:")
    print("1. 运行: python quick_start.py")
    print("2. 或手动启动: python -m uvicorn backend.simple_app:app --host 0.0.0.0 --port 8000")
    print("3. 然后运行: streamlit run frontend/main.py")

if __name__ == "__main__":
    main()
