import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from frontend.utils.logger import get_operation_logs, get_system_logs, clear_old_logs


def show():
    """显示日志查看页面"""
    if not st.session_state.get('authenticated', False):
        st.error("请先登录")
        return
    
    user = st.session_state.get('user', {})
    is_admin = user.get('role') == 'admin'
    
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
    ">
        <h2 style="margin: 0;">📋 系统日志</h2>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">查看系统运行日志和用户操作记录</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 日志类型选择
    tab1, tab2 = st.tabs(["👤 操作日志", "🔧 系统日志"])
    
    with tab1:
        show_operation_logs(is_admin, user.get('id'))
    
    with tab2:
        if is_admin:
            show_system_logs()
        else:
            st.warning("⚠️ 只有管理员可以查看系统日志")


def show_operation_logs(is_admin, user_id):
    """显示操作日志"""
    st.markdown("### 👤 用户操作日志")
    
    # 筛选选项
    col1, col2, col3 = st.columns(3)
    
    with col1:
        log_limit = st.selectbox("显示条数", [50, 100, 200, 500], index=1, key="operation_log_limit")
    
    with col2:
        if is_admin:
            show_all_users = st.checkbox("显示所有用户", value=False)
        else:
            show_all_users = False
    
    with col3:
        if st.button("🔄 刷新日志", key="refresh_operation_logs"):
            st.rerun()
    
    # 获取日志数据
    if is_admin and show_all_users:
        logs = get_operation_logs(limit=log_limit)
    else:
        logs = get_operation_logs(limit=log_limit, user_id=user_id)
    
    if logs:
        # 转换为DataFrame
        df = pd.DataFrame(logs)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp', ascending=False)
        
        # 显示统计信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总操作数", len(logs))
        
        with col2:
            success_count = len(df[df['status'] == 'success'])
            st.metric("成功操作", success_count)
        
        with col3:
            error_count = len(df[df['status'] == 'error'])
            st.metric("失败操作", error_count, delta=f"{'正常' if error_count == 0 else '需关注'}")
        
        with col4:
            unique_users = df['username'].nunique()
            st.metric("活跃用户", unique_users)
        
        # 显示日志表格
        st.markdown("#### 📊 详细日志")
        
        # 格式化显示
        display_df = df[['timestamp', 'username', 'operation', 'module', 'status']].copy()
        display_df['timestamp'] = display_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
        display_df.columns = ['时间', '用户', '操作', '模块', '状态']
        
        # 添加状态颜色
        def color_status(val):
            # 确保val是字符串类型
            if not isinstance(val, str):
                val = str(val) if val is not None else ''

            if val == 'success':
                return 'background-color: #d4edda; color: #155724'
            elif val == 'error':
                return 'background-color: #f8d7da; color: #721c24'
            else:
                return ''
        
        styled_df = display_df.style.applymap(color_status, subset=['状态'])
        st.dataframe(styled_df, use_container_width=True, height=400)
        
        # 导出功能
        if st.button("📤 导出日志"):
            csv = df.to_csv(index=False)
            st.download_button(
                label="下载CSV文件",
                data=csv,
                file_name=f"operation_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
    else:
        st.info("📝 暂无操作日志")


def show_system_logs():
    """显示系统日志"""
    st.markdown("### 🔧 系统日志")
    
    # 筛选选项
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        log_limit = st.selectbox("显示条数", [50, 100, 200, 500], index=1, key="system_log_limit")
    
    with col2:
        log_level = st.selectbox("日志级别", ["全部", "INFO", "WARNING", "ERROR"], index=0, key="system_log_level")
    
    with col3:
        if st.button("🔄 刷新日志", key="refresh_system_logs"):
            st.rerun()
    
    with col4:
        if st.button("🗑️ 清理旧日志", key="clear_old_logs_system"):
            if clear_old_logs(30):
                st.success("✅ 已清理30天前的日志")
                st.rerun()
            else:
                st.error("❌ 清理日志失败")
    
    # 获取日志数据
    level_filter = None if log_level == "全部" else log_level
    logs = get_system_logs(limit=log_limit, level=level_filter)
    
    if logs:
        # 转换为DataFrame
        df = pd.DataFrame(logs)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp', ascending=False)
        
        # 显示统计信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总日志数", len(logs))
        
        with col2:
            info_count = len(df[df['level'] == 'INFO'])
            st.metric("信息日志", info_count)
        
        with col3:
            warning_count = len(df[df['level'] == 'WARNING'])
            st.metric("警告日志", warning_count, delta=f"{'正常' if warning_count == 0 else '需关注'}")
        
        with col4:
            error_count = len(df[df['level'] == 'ERROR'])
            st.metric("错误日志", error_count, delta=f"{'正常' if error_count == 0 else '需处理'}")
        
        # 显示日志表格
        st.markdown("#### 📊 详细日志")
        
        # 格式化显示
        display_df = df[['timestamp', 'level', 'module', 'message']].copy()
        display_df['timestamp'] = display_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
        display_df.columns = ['时间', '级别', '模块', '消息']
        
        # 添加级别颜色
        def color_level(val):
            # 确保val是字符串类型
            if not isinstance(val, str):
                val = str(val) if val is not None else ''

            if val == 'INFO':
                return 'background-color: #d1ecf1; color: #0c5460'
            elif val == 'WARNING':
                return 'background-color: #fff3cd; color: #856404'
            elif val == 'ERROR':
                return 'background-color: #f8d7da; color: #721c24'
            else:
                return ''
        
        styled_df = display_df.style.applymap(color_level, subset=['级别'])
        st.dataframe(styled_df, use_container_width=True, height=400)
        
        # 导出功能
        if st.button("📤 导出系统日志"):
            csv = df.to_csv(index=False)
            st.download_button(
                label="下载CSV文件",
                data=csv,
                file_name=f"system_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
    else:
        st.info("📝 暂无系统日志")
