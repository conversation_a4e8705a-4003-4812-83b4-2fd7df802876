#!/usr/bin/env python3
"""
LLM任务分配系统依赖安装脚本
自动安装系统所需的Python包
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_basic_packages():
    """安装基础包"""
    basic_packages = [
        "fastapi",
        "uvicorn",
        "streamlit",
        "pandas",
        "numpy",
        "requests",
        "plotly",
        "psutil",
        "python-dotenv"
    ]
    
    print("\n📦 安装基础包...")
    for package in basic_packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            print(f"⚠️ {package} 安装失败，但系统可能仍能运行")
    
    return True

def install_optional_packages():
    """安装可选包"""
    optional_packages = [
        "passlib[bcrypt]",
        "python-multipart",
        "sqlmodel",
        "pydantic",
        "pydantic-settings",
        "httpx"
    ]
    
    print("\n📦 安装可选包...")
    for package in optional_packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            print(f"⚠️ {package} 安装失败，某些功能可能不可用")
    
    return True

def install_from_requirements():
    """从requirements.txt安装"""
    requirements_file = Path("requirements.txt")
    
    if requirements_file.exists():
        print("\n📋 从requirements.txt安装依赖...")
        return run_command("pip install -r requirements.txt", "安装requirements.txt中的依赖")
    else:
        print("⚠️ requirements.txt文件不存在，使用手动安装")
        return False

def upgrade_pip():
    """升级pip"""
    return run_command("python -m pip install --upgrade pip", "升级pip")

def create_virtual_env():
    """创建虚拟环境（可选）"""
    response = input("\n❓ 是否要创建虚拟环境？(y/n): ").lower().strip()
    
    if response == 'y':
        venv_name = "llm_system_env"
        if run_command(f"python -m venv {venv_name}", f"创建虚拟环境 {venv_name}"):
            print(f"\n✅ 虚拟环境已创建: {venv_name}")
            print("💡 激活虚拟环境:")
            if os.name == 'nt':  # Windows
                print(f"   {venv_name}\\Scripts\\activate")
            else:  # Linux/Mac
                print(f"   source {venv_name}/bin/activate")
            print("   然后重新运行此安装脚本")
            return True
    
    return False

def check_installation():
    """检查安装结果"""
    print("\n🔍 检查安装结果...")
    
    test_imports = [
        ("streamlit", "Streamlit"),
        ("fastapi", "FastAPI"),
        ("pandas", "Pandas"),
        ("numpy", "NumPy"),
        ("requests", "Requests"),
        ("plotly", "Plotly"),
        ("psutil", "psutil")
    ]
    
    success_count = 0
    total_count = len(test_imports)
    
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name} 可用")
            success_count += 1
        except ImportError:
            print(f"❌ {name} 不可用")
    
    print(f"\n📊 安装结果: {success_count}/{total_count} 个包可用")
    
    if success_count >= 5:  # 至少需要基础包
        print("✅ 基础依赖已满足，系统可以运行")
        return True
    else:
        print("❌ 缺少关键依赖，系统可能无法正常运行")
        return False

def main():
    """主函数"""
    print("🚁 LLM任务分配系统 - 依赖安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 询问是否创建虚拟环境
    if create_virtual_env():
        return
    
    # 升级pip
    upgrade_pip()
    
    # 尝试从requirements.txt安装
    if not install_from_requirements():
        # 如果失败，手动安装
        install_basic_packages()
        
        # 询问是否安装可选包
        response = input("\n❓ 是否安装可选包（用于高级功能）？(y/n): ").lower().strip()
        if response == 'y':
            install_optional_packages()
    
    # 检查安装结果
    if check_installation():
        print("\n🎉 依赖安装完成！")
        print("\n🚀 现在可以运行系统:")
        print("   python start.py")
        print("\n或者直接运行前端:")
        print("   streamlit run frontend/main.py")
    else:
        print("\n⚠️ 安装过程中遇到问题")
        print("💡 建议:")
        print("   1. 检查网络连接")
        print("   2. 尝试使用虚拟环境")
        print("   3. 手动安装缺失的包")

if __name__ == "__main__":
    main()
