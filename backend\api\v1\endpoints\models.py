from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session, select

from backend.core.database import get_session
from backend.core.security import get_current_user
from backend.models.user import User
from backend.models.model_config import ModelConfig, ModelConfigCreate, ModelConfigUpdate, ModelConfigRead, ModelConfigSummary

router = APIRouter()


@router.get("/", response_model=List[ModelConfigSummary])
async def get_models(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """获取模型列表"""
    statement = select(ModelConfig).where(ModelConfig.owner_id == current_user.id).offset(skip).limit(limit)
    models = session.exec(statement).all()
    
    # 转换为摘要格式
    summaries = []
    for model in models:
        summary = ModelConfigSummary(
            id=model.id,
            name=model.name,
            model_type=model.model_type,
            version=model.version,
            status=model.status,
            accuracy=model.metrics.accuracy if model.metrics else 0.0,
            usage_count=model.usage_count,
            created_at=model.created_at,
            tags=model.tags
        )
        summaries.append(summary)
    
    return summaries


@router.post("/", response_model=ModelConfigRead)
async def create_model(
    model_data: ModelConfigCreate,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """创建模型配置"""
    model = ModelConfig(
        **model_data.dict(),
        owner_id=current_user.id
    )
    
    session.add(model)
    session.commit()
    session.refresh(model)
    
    return model


@router.get("/{model_id}", response_model=ModelConfigRead)
async def get_model(
    model_id: int,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """获取模型详情"""
    statement = select(ModelConfig).where(
        ModelConfig.id == model_id,
        ModelConfig.owner_id == current_user.id
    )
    model = session.exec(statement).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    return model


@router.put("/{model_id}", response_model=ModelConfigRead)
async def update_model(
    model_id: int,
    model_update: ModelConfigUpdate,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """更新模型配置"""
    statement = select(ModelConfig).where(
        ModelConfig.id == model_id,
        ModelConfig.owner_id == current_user.id
    )
    model = session.exec(statement).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    model_data = model_update.dict(exclude_unset=True)
    for field, value in model_data.items():
        setattr(model, field, value)
    
    from datetime import datetime
    model.updated_at = datetime.utcnow()
    
    session.add(model)
    session.commit()
    session.refresh(model)
    
    return model


@router.delete("/{model_id}")
async def delete_model(
    model_id: int,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """删除模型配置"""
    statement = select(ModelConfig).where(
        ModelConfig.id == model_id,
        ModelConfig.owner_id == current_user.id
    )
    model = session.exec(statement).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    session.delete(model)
    session.commit()
    
    return {"message": "Model deleted successfully"}


@router.post("/{model_id}/load")
async def load_model(
    model_id: int,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """加载模型"""
    statement = select(ModelConfig).where(
        ModelConfig.id == model_id,
        ModelConfig.owner_id == current_user.id
    )
    model = session.exec(statement).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    # 这里应该调用模型加载逻辑
    # 暂时返回成功消息
    return {"message": "Model loading started", "model_id": model_id}


@router.post("/{model_id}/unload")
async def unload_model(
    model_id: int,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """卸载模型"""
    statement = select(ModelConfig).where(
        ModelConfig.id == model_id,
        ModelConfig.owner_id == current_user.id
    )
    model = session.exec(statement).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    # 这里应该调用模型卸载逻辑
    # 暂时返回成功消息
    return {"message": "Model unloaded successfully", "model_id": model_id}
