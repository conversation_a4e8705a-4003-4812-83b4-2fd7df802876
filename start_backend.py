#!/usr/bin/env python3
"""
后端服务启动脚本
单独启动FastAPI后端服务
"""

import os
import sys
import subprocess
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
os.chdir(PROJECT_ROOT)

# 添加项目路径到Python路径
sys.path.insert(0, str(PROJECT_ROOT))

def check_backend_dependencies():
    """检查后端依赖"""
    print("🔍 检查后端依赖...")
    
    required_packages = {
        'fastapi': 'FastAPI框架',
        'uvicorn': 'ASGI服务器'
    }
    
    missing_packages = []
    
    for package, description in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} ({description})")
        except ImportError:
            print(f"❌ {package} ({description}) - 缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少后端依赖: {', '.join(missing_packages)}")
        print("💡 安装命令:")
        for package in missing_packages:
            print(f"   pip install {package}")
        return False
    
    print("✅ 后端依赖检查通过")
    return True

def setup_directories():
    """设置目录结构"""
    print("📁 设置目录结构...")
    
    directories = [
        'data',
        'logs',
        'models',
        'static'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构设置完成")

def create_simple_backend():
    """创建简化的后端应用"""
    print("🔧 创建简化后端...")

    simple_main_content = '''
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(
    title="LLM任务分配系统API",
    description="无人机干扰决策系统后端API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "LLM任务分配系统API", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "backend"}

@app.get("/api/v1/health")
async def api_health():
    return {"status": "healthy", "version": "1.0.0"}
'''

    # 保存简化版本
    simple_main_path = PROJECT_ROOT / "backend/app/main_simple.py"
    simple_main_path.parent.mkdir(parents=True, exist_ok=True)

    with open(simple_main_path, 'w', encoding='utf-8') as f:
        f.write(simple_main_content)

    print(f"✅ 简化后端已创建")
    return simple_main_path

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")

    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONPATH': str(PROJECT_ROOT),
        'DATABASE_URL': 'sqlite:///./data/app.db',
        'SECRET_KEY': 'dev-secret-key-change-in-production',
        'ENVIRONMENT': 'development',
        'DEBUG': 'True'
    })

    # 首先尝试启动完整版本
    cmd = [
        sys.executable, "-m", "uvicorn",
        "backend.app.main:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ]

    try:
        print("📍 后端API: http://localhost:8000")
        print("📍 API文档: http://localhost:8000/docs")
        print("🛑 按 Ctrl+C 停止服务")

        subprocess.run(cmd, env=env, cwd=PROJECT_ROOT)

    except KeyboardInterrupt:
        print("\n🛑 后端服务已停止")
    except Exception as e:
        print(f"❌ 完整后端启动失败: {e}")
        print("🔄 尝试启动简化版本...")

        # 创建并启动简化版本
        create_simple_backend()

        simple_cmd = [
            sys.executable, "-m", "uvicorn",
            "backend.app.main_simple:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]

        try:
            print("📍 简化后端API: http://localhost:8000")
            print("📍 健康检查: http://localhost:8000/health")
            print("🛑 按 Ctrl+C 停止服务")

            subprocess.run(simple_cmd, env=env, cwd=PROJECT_ROOT)

        except KeyboardInterrupt:
            print("\n🛑 简化后端服务已停止")
        except Exception as e2:
            print(f"❌ 简化后端也启动失败: {e2}")
            print("💡 建议运行: python fix_backend.py")

def main():
    """主函数"""
    print("🚁 LLM任务分配系统 - 后端服务启动脚本")
    print("=" * 50)
    
    # 设置目录
    setup_directories()
    
    # 检查依赖
    if not check_backend_dependencies():
        print("\n💡 建议:")
        print("   1. 运行修复脚本: python fix_backend.py")
        print("   2. 或手动安装: pip install fastapi uvicorn")
        print("   3. 或运行完整安装: python install_dependencies.py")

        response = input("\n❓ 是否仍要尝试启动？(y/n): ").lower().strip()
        if response != 'y':
            sys.exit(1)

    # 启动后端服务
    start_backend()

if __name__ == "__main__":
    main()
