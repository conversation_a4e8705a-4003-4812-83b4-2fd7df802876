from sqlmodel import SQLModel, Field, Relationship, JSON, Column
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class ModelType(str, Enum):
    """模型类型枚举"""
    GPT = "gpt"
    BERT = "bert"
    T5 = "t5"
    LLAMA = "llama"
    CUSTOM = "custom"


class ModelStatus(str, Enum):
    """模型状态枚举"""
    DRAFT = "draft"
    TRAINING = "training"
    READY = "ready"
    DEPLOYED = "deployed"
    DEPRECATED = "deprecated"


class ModelMetrics(SQLModel):
    """模型指标模型"""
    accuracy: float = Field(ge=0, le=1, default=0.0)
    precision: float = Field(ge=0, le=1, default=0.0)
    recall: float = Field(ge=0, le=1, default=0.0)
    f1_score: float = Field(ge=0, le=1, default=0.0)
    inference_time: float = Field(ge=0, default=0.0)  # 推理时间（秒）
    memory_usage: float = Field(ge=0, default=0.0)  # 内存使用（MB）
    energy_efficiency: float = Field(ge=0, le=1, default=0.0)
    robustness_score: float = Field(ge=0, le=1, default=0.0)


class TrainingConfig(SQLModel):
    """训练配置模型"""
    learning_rate: float = Field(ge=0, default=1e-4)
    batch_size: int = Field(ge=1, default=32)
    epochs: int = Field(ge=1, default=10)
    optimizer: str = Field(default="adam")
    loss_function: str = Field(default="cross_entropy")
    regularization: Dict[str, float] = Field(default_factory=dict)
    early_stopping: bool = Field(default=True)
    validation_split: float = Field(ge=0, le=1, default=0.2)


class ModelConfigBase(SQLModel):
    """模型配置基础模型"""
    name: str = Field(min_length=1, max_length=100)
    description: Optional[str] = Field(default=None, max_length=500)
    model_type: ModelType
    version: str = Field(default="1.0.0")
    status: ModelStatus = Field(default=ModelStatus.DRAFT)
    
    # 模型参数
    model_path: Optional[str] = Field(default=None)
    tokenizer_path: Optional[str] = Field(default=None)
    config_path: Optional[str] = Field(default=None)
    
    # 模型规格
    model_size: Optional[float] = Field(default=None, ge=0)  # 模型大小（GB）
    parameter_count: Optional[int] = Field(default=None, ge=0)  # 参数数量
    context_length: int = Field(default=2048, ge=1)  # 上下文长度
    
    # 推理配置
    max_tokens: int = Field(default=512, ge=1)
    temperature: float = Field(default=0.7, ge=0, le=2)
    top_p: float = Field(default=0.9, ge=0, le=1)
    top_k: int = Field(default=50, ge=1)
    
    # 任务特定配置
    task_specific_params: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))
    preprocessing_config: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))
    postprocessing_config: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))


class ModelConfig(ModelConfigBase, table=True):
    """模型配置数据库模型"""
    id: Optional[int] = Field(default=None, primary_key=True)
    owner_id: int = Field(foreign_key="user.id")
    
    # 训练相关
    training_config: Optional[TrainingConfig] = Field(default=None, sa_column=Column(JSON))
    training_data_path: Optional[str] = Field(default=None)
    validation_data_path: Optional[str] = Field(default=None)
    
    # 性能指标
    metrics: ModelMetrics = Field(default_factory=ModelMetrics, sa_column=Column(JSON))
    benchmark_results: Dict[str, float] = Field(default_factory=dict, sa_column=Column(JSON))
    
    # 版本控制
    parent_model_id: Optional[int] = Field(default=None, foreign_key="modelconfig.id")
    tags: List[str] = Field(default_factory=list, sa_column=Column(JSON))
    
    # 使用统计
    usage_count: int = Field(default=0, ge=0)
    last_used: Optional[datetime] = Field(default=None)
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    trained_at: Optional[datetime] = Field(default=None)
    
    # 关联关系
    owner: "User" = Relationship(back_populates="model_configs")
    tasks: List["Task"] = Relationship(back_populates="model_config")
    parent_model: Optional["ModelConfig"] = Relationship(
        back_populates="child_models",
        sa_relationship_kwargs={"remote_side": "ModelConfig.id"}
    )
    child_models: List["ModelConfig"] = Relationship(back_populates="parent_model")


class ModelConfigCreate(ModelConfigBase):
    """创建模型配置模型"""
    training_config: Optional[TrainingConfig] = None
    training_data_path: Optional[str] = None
    validation_data_path: Optional[str] = None
    parent_model_id: Optional[int] = None
    tags: List[str] = Field(default_factory=list)


class ModelConfigUpdate(SQLModel):
    """更新模型配置模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=100)
    description: Optional[str] = Field(default=None, max_length=500)
    status: Optional[ModelStatus] = Field(default=None)
    version: Optional[str] = Field(default=None)
    
    # 推理配置
    max_tokens: Optional[int] = Field(default=None, ge=1)
    temperature: Optional[float] = Field(default=None, ge=0, le=2)
    top_p: Optional[float] = Field(default=None, ge=0, le=1)
    top_k: Optional[int] = Field(default=None, ge=1)
    
    # 配置参数
    task_specific_params: Optional[Dict[str, Any]] = Field(default=None)
    preprocessing_config: Optional[Dict[str, Any]] = Field(default=None)
    postprocessing_config: Optional[Dict[str, Any]] = Field(default=None)
    
    # 标签
    tags: Optional[List[str]] = Field(default=None)


class ModelConfigRead(ModelConfigBase):
    """读取模型配置模型"""
    id: int
    owner_id: int
    training_config: Optional[TrainingConfig]
    training_data_path: Optional[str]
    validation_data_path: Optional[str]
    metrics: ModelMetrics
    benchmark_results: Dict[str, float]
    parent_model_id: Optional[int]
    tags: List[str]
    usage_count: int
    last_used: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    trained_at: Optional[datetime]


class ModelConfigSummary(SQLModel):
    """模型配置摘要模型"""
    id: int
    name: str
    model_type: ModelType
    version: str
    status: ModelStatus
    accuracy: float
    usage_count: int
    created_at: datetime
    tags: List[str]


# 导入其他模型以避免循环导入
from backend.models.user import User
from backend.models.task import Task
