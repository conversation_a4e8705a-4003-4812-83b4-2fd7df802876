import streamlit as st
import requests
import json
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from frontend.utils.logger import log_operation


def show():
    """显示API文档页面"""
    if not st.session_state.get('authenticated', False):
        st.error("请先登录")
        return
    

    
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
    ">
        <h2 style="margin: 0;">🔧 API文档与开发者工具</h2>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">API接口文档、测试工具和开发者资源</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 功能选项卡
    tab1, tab2 = st.tabs(["📚 API文档", "🧪 API测试"])

    with tab1:
        show_api_documentation()

    with tab2:
        show_api_testing()


def show_api_documentation():
    """显示API文档"""
    st.markdown("### 📚 API接口文档")
    
    # API概览
    st.markdown("#### 🌐 API概览")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("API版本", "v1.0")
    
    with col2:
        st.metric("基础URL", "http://localhost:8000")
    
    with col3:
        # 检查API状态
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            api_status = "在线" if response.status_code == 200 else "离线"
        except:
            api_status = "离线"
        st.metric("API状态", api_status)
    
    # 认证说明
    st.markdown("#### 🔐 认证方式")
    st.code("""
# 获取访问令牌
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "your_username",
    "password": "your_password"
}

# 使用令牌访问API
GET /api/v1/protected_endpoint
Authorization: Bearer <your_token>
    """, language="http")
    
    # API端点列表
    st.markdown("#### 📋 API端点列表")
    
    api_endpoints = [
        {
            "method": "POST",
            "endpoint": "/api/v1/auth/login",
            "description": "用户登录",
            "auth": "否"
        },
        {
            "method": "POST",
            "endpoint": "/api/v1/auth/register",
            "description": "用户注册",
            "auth": "否"
        },
        {
            "method": "GET",
            "endpoint": "/api/v1/users/me",
            "description": "获取当前用户信息",
            "auth": "是"
        },
        {
            "method": "GET",
            "endpoint": "/api/v1/scenarios",
            "description": "获取场景列表",
            "auth": "是"
        },
        {
            "method": "POST",
            "endpoint": "/api/v1/scenarios",
            "description": "创建新场景",
            "auth": "是"
        },
        {
            "method": "GET",
            "endpoint": "/api/v1/tasks",
            "description": "获取任务列表",
            "auth": "是"
        },
        {
            "method": "POST",
            "endpoint": "/api/v1/tasks",
            "description": "创建新任务",
            "auth": "是"
        },
        {
            "method": "GET",
            "endpoint": "/api/v1/models",
            "description": "获取模型列表",
            "auth": "是"
        },
        {
            "method": "POST",
            "endpoint": "/api/v1/models/inference",
            "description": "执行模型推理",
            "auth": "是"
        }
    ]
    
    # 创建API端点表格
    import pandas as pd
    df = pd.DataFrame(api_endpoints)
    
    # 添加方法颜色
    def color_method(val):
        # 确保val是字符串类型
        if not isinstance(val, str):
            val = str(val) if val is not None else ''

        colors = {
            'GET': 'background-color: #d4edda; color: #155724',
            'POST': 'background-color: #cce5ff; color: #004085',
            'PUT': 'background-color: #fff3cd; color: #856404',
            'DELETE': 'background-color: #f8d7da; color: #721c24'
        }
        return colors.get(val, '')
    
    styled_df = df.style.applymap(color_method, subset=['method'])
    st.dataframe(styled_df, use_container_width=True)
    
    # 详细API示例
    st.markdown("#### 📝 API使用示例")
    
    with st.expander("用户认证示例", expanded=False):
        st.code("""
# Python示例
import requests

# 登录获取令牌
login_data = {
    "username": "admin",
    "password": "admin123"
}

response = requests.post(
    "http://localhost:8000/api/v1/auth/login",
    json=login_data
)

if response.status_code == 200:
    token = response.json()["access_token"]
    
    # 使用令牌访问受保护的API
    headers = {"Authorization": f"Bearer {token}"}
    
    user_info = requests.get(
        "http://localhost:8000/api/v1/users/me",
        headers=headers
    )
    
    print(user_info.json())
        """, language="python")
    
    with st.expander("场景管理示例", expanded=False):
        st.code("""
# 创建新场景
scenario_data = {
    "name": "测试场景",
    "description": "这是一个测试场景",
    "map_size": "10x10",
    "drone_count": 5,
    "buoy_count": 10,
    "communication_range": 3.0,
    "interference_strength": 0.8
}

response = requests.post(
    "http://localhost:8000/api/v1/scenarios",
    json=scenario_data,
    headers=headers
)

# 获取场景列表
scenarios = requests.get(
    "http://localhost:8000/api/v1/scenarios",
    headers=headers
)

print(scenarios.json())
        """, language="python")
    
    # 错误代码说明
    st.markdown("#### ⚠️ 错误代码说明")
    
    error_codes = [
        {"code": 200, "message": "OK", "description": "请求成功"},
        {"code": 400, "message": "Bad Request", "description": "请求参数错误"},
        {"code": 401, "message": "Unauthorized", "description": "未授权，需要登录"},
        {"code": 403, "message": "Forbidden", "description": "禁止访问，权限不足"},
        {"code": 404, "message": "Not Found", "description": "资源不存在"},
        {"code": 422, "message": "Unprocessable Entity", "description": "请求格式正确但语义错误"},
        {"code": 500, "message": "Internal Server Error", "description": "服务器内部错误"}
    ]
    
    error_df = pd.DataFrame(error_codes)
    st.dataframe(error_df, use_container_width=True)


def show_api_testing():
    """显示API测试工具"""
    st.markdown("### 🧪 API测试工具")
    
    # API测试表单
    st.markdown("#### 🔧 接口测试")
    
    col1, col2 = st.columns([1, 2])
    
    with col1:
        method = st.selectbox(
            "请求方法",
            ["GET", "POST", "PUT", "DELETE"],
            key="api_test_method"
        )
        
        endpoint = st.text_input(
            "API端点",
            value="/api/v1/health",
            help="相对于基础URL的端点路径"
        )
        
        auth_required = st.checkbox("需要认证", value=False)
        
        if auth_required:
            token = st.text_input(
                "访问令牌",
                type="password",
                help="Bearer token"
            )
    
    with col2:
        st.markdown("**请求体 (JSON格式):**")
        request_body = st.text_area(
            "请求体",
            value='{\n  "key": "value"\n}',
            height=150,
            help="POST/PUT请求的JSON数据"
        )
        
        st.markdown("**请求头:**")
        custom_headers = st.text_area(
            "自定义请求头",
            value='{\n  "Content-Type": "application/json"\n}',
            height=100,
            help="额外的HTTP请求头"
        )
    
    # 发送请求按钮
    if st.button("🚀 发送请求", type="primary"):
        try:
            # 构建完整URL
            base_url = "http://localhost:8000"
            full_url = f"{base_url}{endpoint}"
            
            # 构建请求头
            headers = {"Content-Type": "application/json"}
            
            # 添加自定义请求头
            try:
                custom_headers_dict = json.loads(custom_headers)
                headers.update(custom_headers_dict)
            except:
                st.warning("自定义请求头格式错误，使用默认请求头")
            
            # 添加认证头
            if auth_required and token:
                headers["Authorization"] = f"Bearer {token}"
            
            # 构建请求数据
            data = None
            if method in ["POST", "PUT"] and request_body.strip():
                try:
                    data = json.loads(request_body)
                except:
                    st.error("请求体JSON格式错误")
                    return
            
            # 发送请求
            if method == "GET":
                response = requests.get(full_url, headers=headers, timeout=10)
            elif method == "POST":
                response = requests.post(full_url, json=data, headers=headers, timeout=10)
            elif method == "PUT":
                response = requests.put(full_url, json=data, headers=headers, timeout=10)
            elif method == "DELETE":
                response = requests.delete(full_url, headers=headers, timeout=10)
            
            # 显示响应
            st.markdown("#### 📥 响应结果")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                status_color = "🟢" if response.status_code < 400 else "🔴"
                st.metric("状态码", f"{status_color} {response.status_code}")
            
            with col2:
                st.metric("响应时间", f"{response.elapsed.total_seconds():.3f}s")
            
            with col3:
                content_length = len(response.content)
                st.metric("响应大小", f"{content_length} bytes")
            
            # 响应头
            st.markdown("**响应头:**")
            response_headers = dict(response.headers)
            st.json(response_headers)
            
            # 响应体
            st.markdown("**响应体:**")
            try:
                response_json = response.json()
                st.json(response_json)
            except:
                st.code(response.text)
            
            # 记录API测试日志
            log_operation(
                "API接口测试",
                "开发者工具",
                {
                    "method": method,
                    "endpoint": endpoint,
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds()
                }
            )
            
        except requests.exceptions.RequestException as e:
            st.error(f"请求失败: {e}")
        except Exception as e:
            st.error(f"测试过程中发生错误: {e}")
    
    # 快速测试按钮
    st.markdown("#### ⚡ 快速测试")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🏥 健康检查", use_container_width=True):
            try:
                response = requests.get("http://localhost:8000/health", timeout=5)
                if response.status_code == 200:
                    st.success("✅ API服务正常")
                    st.json(response.json())
                else:
                    st.error(f"❌ API服务异常: {response.status_code}")
            except:
                st.error("❌ 无法连接到API服务")
    
    with col2:
        if st.button("📚 API文档", use_container_width=True):
            try:
                response = requests.get("http://localhost:8000/docs", timeout=5)
                if response.status_code == 200:
                    st.success("✅ API文档可访问")
                    st.info("🔗 访问地址: http://localhost:8000/docs")
                else:
                    st.error("❌ API文档不可访问")
            except:
                st.error("❌ 无法访问API文档")
    
    with col3:
        if st.button("🔧 OpenAPI规范", use_container_width=True):
            try:
                response = requests.get("http://localhost:8000/openapi.json", timeout=5)
                if response.status_code == 200:
                    st.success("✅ OpenAPI规范可访问")
                    with st.expander("查看OpenAPI规范"):
                        st.json(response.json())
                else:
                    st.error("❌ OpenAPI规范不可访问")
            except:
                st.error("❌ 无法获取OpenAPI规范")



