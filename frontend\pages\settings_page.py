import streamlit as st
import json
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from frontend.utils.logger import log_operation


def load_system_settings():
    """加载系统设置"""
    settings_path = Path("data/system_settings.json")
    if settings_path.exists():
        with open(settings_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    # 默认设置
    return {
        "theme": {
            "primary_color": "#667eea",
            "secondary_color": "#764ba2",
            "background_color": "#ffffff",
            "text_color": "#000000"
        },
        "system": {
            "language": "zh-CN",
            "timezone": "Asia/Shanghai",
            "date_format": "YYYY-MM-DD",
            "time_format": "24h"
        },
        "performance": {
            "max_concurrent_tasks": 10,
            "cache_size": 100,
            "log_retention_days": 30,
            "auto_backup": True,
            "backup_interval": 24
        },
        "security": {
            "session_timeout": 60,
            "password_min_length": 6,
            "max_login_attempts": 5,
            "enable_2fa": False
        },
        "notifications": {
            "email_enabled": False,
            "email_server": "",
            "email_port": 587,
            "email_username": "",
            "email_password": ""
        }
    }


def save_system_settings(settings):
    """保存系统设置"""
    settings_path = Path("data/system_settings.json")
    settings_path.parent.mkdir(exist_ok=True)
    
    with open(settings_path, 'w', encoding='utf-8') as f:
        json.dump(settings, f, ensure_ascii=False, indent=2)


def show():
    """显示系统设置页面"""
    if not st.session_state.get('authenticated', False):
        st.error("请先登录")
        return
    
    user = st.session_state.get('user', {})
    is_admin = user.get('role') == 'admin'
    
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
    ">
        <h2 style="margin: 0;">⚙️ 系统设置</h2>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">个性化配置和系统参数设置</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 设置选项卡
    if is_admin:
        tab1, tab2, tab3, tab4, tab5 = st.tabs(["🎨 界面设置", "🌐 系统设置", "⚡ 性能设置", "🔒 安全设置", "📧 通知设置"])
    else:
        tab1, tab2 = st.tabs(["🎨 界面设置", "👤 个人设置"])
    
    settings = load_system_settings()
    
    with tab1:
        show_theme_settings(settings)
    
    if is_admin:
        with tab2:
            show_system_settings(settings)
        
        with tab3:
            show_performance_settings(settings)
        
        with tab4:
            show_security_settings(settings)
        
        with tab5:
            show_notification_settings(settings)
    else:
        with tab2:
            show_personal_settings()


def show_theme_settings(settings):
    """显示主题设置"""
    st.markdown("### 🎨 界面主题设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 颜色配置")
        
        primary_color = st.color_picker(
            "主色调",
            value=settings["theme"]["primary_color"],
            help="系统主要颜色"
        )
        
        secondary_color = st.color_picker(
            "辅助色",
            value=settings["theme"]["secondary_color"],
            help="系统辅助颜色"
        )
        
        background_color = st.color_picker(
            "背景色",
            value=settings["theme"]["background_color"],
            help="页面背景颜色"
        )
        
        text_color = st.color_picker(
            "文字色",
            value=settings["theme"]["text_color"],
            help="文字颜色"
        )
    
    with col2:
        st.markdown("#### 预览效果")
        
        # 主题预览
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, {primary_color} 0%, {secondary_color} 100%);
            padding: 1rem;
            border-radius: 10px;
            color: white;
            margin-bottom: 1rem;
        ">
            <h4 style="margin: 0;">主题预览</h4>
            <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">这是主题色彩效果预览</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown(f"""
        <div style="
            background: {background_color};
            color: {text_color};
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        ">
            <p style="margin: 0;">这是背景和文字颜色预览</p>
        </div>
        """, unsafe_allow_html=True)
    
    if st.button("💾 保存主题设置", type="primary"):
        settings["theme"]["primary_color"] = primary_color
        settings["theme"]["secondary_color"] = secondary_color
        settings["theme"]["background_color"] = background_color
        settings["theme"]["text_color"] = text_color
        
        save_system_settings(settings)
        log_operation("更新主题设置", "系统设置")
        st.success("✅ 主题设置已保存！")
        st.rerun()


def show_system_settings(settings):
    """显示系统设置"""
    st.markdown("### 🌐 系统基础设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        language = st.selectbox(
            "系统语言",
            ["zh-CN", "en-US"],
            index=0 if settings["system"]["language"] == "zh-CN" else 1,
            help="系统界面语言",
            key="system_language"
        )

        timezone = st.selectbox(
            "时区设置",
            ["Asia/Shanghai", "UTC", "America/New_York", "Europe/London"],
            index=0,
            help="系统时区",
            key="system_timezone"
        )
    
    with col2:
        date_format = st.selectbox(
            "日期格式",
            ["YYYY-MM-DD", "MM/DD/YYYY", "DD/MM/YYYY"],
            index=0,
            help="日期显示格式",
            key="system_date_format"
        )

        time_format = st.selectbox(
            "时间格式",
            ["24h", "12h"],
            index=0 if settings["system"]["time_format"] == "24h" else 1,
            help="时间显示格式",
            key="system_time_format"
        )
    
    if st.button("💾 保存系统设置", type="primary"):
        settings["system"]["language"] = language
        settings["system"]["timezone"] = timezone
        settings["system"]["date_format"] = date_format
        settings["system"]["time_format"] = time_format
        
        save_system_settings(settings)
        log_operation("更新系统设置", "系统设置")
        st.success("✅ 系统设置已保存！")


def show_performance_settings(settings):
    """显示性能设置"""
    st.markdown("### ⚡ 性能优化设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        max_concurrent_tasks = st.number_input(
            "最大并发任务数",
            min_value=1,
            max_value=50,
            value=settings["performance"]["max_concurrent_tasks"],
            help="同时处理的最大任务数量"
        )
        
        cache_size = st.number_input(
            "缓存大小 (MB)",
            min_value=10,
            max_value=1000,
            value=settings["performance"]["cache_size"],
            help="系统缓存大小"
        )
        
        log_retention_days = st.number_input(
            "日志保留天数",
            min_value=1,
            max_value=365,
            value=settings["performance"]["log_retention_days"],
            help="系统日志保留时间"
        )
    
    with col2:
        auto_backup = st.checkbox(
            "自动备份",
            value=settings["performance"]["auto_backup"],
            help="启用自动数据备份"
        )
        
        backup_interval = st.number_input(
            "备份间隔 (小时)",
            min_value=1,
            max_value=168,
            value=settings["performance"]["backup_interval"],
            help="自动备份时间间隔",
            disabled=not auto_backup
        )
    
    if st.button("💾 保存性能设置", type="primary"):
        settings["performance"]["max_concurrent_tasks"] = max_concurrent_tasks
        settings["performance"]["cache_size"] = cache_size
        settings["performance"]["log_retention_days"] = log_retention_days
        settings["performance"]["auto_backup"] = auto_backup
        settings["performance"]["backup_interval"] = backup_interval
        
        save_system_settings(settings)
        log_operation("更新性能设置", "系统设置")
        st.success("✅ 性能设置已保存！")


def show_security_settings(settings):
    """显示安全设置"""
    st.markdown("### 🔒 安全策略设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        session_timeout = st.number_input(
            "会话超时 (分钟)",
            min_value=5,
            max_value=480,
            value=settings["security"]["session_timeout"],
            help="用户会话超时时间"
        )
        
        password_min_length = st.number_input(
            "密码最小长度",
            min_value=4,
            max_value=20,
            value=settings["security"]["password_min_length"],
            help="用户密码最小长度要求"
        )
    
    with col2:
        max_login_attempts = st.number_input(
            "最大登录尝试次数",
            min_value=3,
            max_value=10,
            value=settings["security"]["max_login_attempts"],
            help="登录失败锁定前的最大尝试次数"
        )
        
        enable_2fa = st.checkbox(
            "启用双因素认证",
            value=settings["security"]["enable_2fa"],
            help="启用双因素身份验证"
        )
    
    if st.button("💾 保存安全设置", type="primary"):
        settings["security"]["session_timeout"] = session_timeout
        settings["security"]["password_min_length"] = password_min_length
        settings["security"]["max_login_attempts"] = max_login_attempts
        settings["security"]["enable_2fa"] = enable_2fa
        
        save_system_settings(settings)
        log_operation("更新安全设置", "系统设置")
        st.success("✅ 安全设置已保存！")


def show_notification_settings(settings):
    """显示通知设置"""
    st.markdown("### 📧 通知配置设置")
    
    email_enabled = st.checkbox(
        "启用邮件通知",
        value=settings["notifications"]["email_enabled"],
        help="启用系统邮件通知功能"
    )
    
    if email_enabled:
        col1, col2 = st.columns(2)
        
        with col1:
            email_server = st.text_input(
                "SMTP服务器",
                value=settings["notifications"]["email_server"],
                help="邮件服务器地址"
            )
            
            email_port = st.number_input(
                "SMTP端口",
                min_value=1,
                max_value=65535,
                value=settings["notifications"]["email_port"],
                help="邮件服务器端口"
            )
        
        with col2:
            email_username = st.text_input(
                "邮箱用户名",
                value=settings["notifications"]["email_username"],
                help="发送邮件的用户名"
            )
            
            email_password = st.text_input(
                "邮箱密码",
                type="password",
                value=settings["notifications"]["email_password"],
                help="发送邮件的密码"
            )
        
        if st.button("📧 测试邮件发送", key="test_email_btn"):
            st.success("✅ 邮件测试功能已启用，请检查邮箱配置")
    
    if st.button("💾 保存通知设置", type="primary"):
        settings["notifications"]["email_enabled"] = email_enabled
        if email_enabled:
            settings["notifications"]["email_server"] = email_server
            settings["notifications"]["email_port"] = email_port
            settings["notifications"]["email_username"] = email_username
            settings["notifications"]["email_password"] = email_password
        
        save_system_settings(settings)
        log_operation("更新通知设置", "系统设置")
        st.success("✅ 通知设置已保存！")


def show_personal_settings():
    """显示个人设置"""
    st.markdown("### 👤 个人偏好设置")
    
    user = st.session_state.get('user', {})
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 显示偏好")
        
        show_tooltips = st.checkbox("显示工具提示", value=True)
        show_animations = st.checkbox("启用动画效果", value=True)
        compact_mode = st.checkbox("紧凑模式", value=False)
    
    with col2:
        st.markdown("#### 通知偏好")
        
        email_notifications = st.checkbox("邮件通知", value=False)
        browser_notifications = st.checkbox("浏览器通知", value=True)
        sound_notifications = st.checkbox("声音提醒", value=False)
    
    if st.button("💾 保存个人设置", type="primary"):
        # 这里可以保存用户个人设置到数据库
        log_operation("更新个人设置", "用户设置")
        st.success("✅ 个人设置已保存！")
