version: '3.8'

services:
  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: llm_task_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - llm_network

  # 后端API服务
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: llm_task_backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/app.db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
      - ENVIRONMENT=production
    volumes:
      - ./data:/app/data
      - ./models:/app/models
    depends_on:
      - redis
    networks:
      - llm_network
    restart: unless-stopped

  # Celery异步任务处理
  celery:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: llm_task_celery
    command: celery -A backend.app.celery worker --loglevel=info
    environment:
      - DATABASE_URL=sqlite:///./data/app.db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
    volumes:
      - ./data:/app/data
      - ./models:/app/models
    depends_on:
      - redis
      - backend
    networks:
      - llm_network
    restart: unless-stopped

  # 前端Streamlit服务
  frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
    container_name: llm_task_frontend
    ports:
      - "8501:8501"
    environment:
      - BACKEND_URL=http://backend:8000
      - REDIS_URL=redis://redis:6379/1
    volumes:
      - ./frontend:/app/frontend
      - ./data:/app/data
    depends_on:
      - backend
    networks:
      - llm_network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: llm_task_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - llm_network
    restart: unless-stopped

volumes:
  redis_data:
  app_data:

networks:
  llm_network:
    driver: bridge
