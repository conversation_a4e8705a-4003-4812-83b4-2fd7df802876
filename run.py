#!/usr/bin/env python3
"""
LLM任务分配系统 - 一键启动脚本
最简单的启动方式
"""

import subprocess
import sys
import time
import threading
import signal
from pathlib import Path

# 全局进程列表
processes = []

def cleanup():
    """清理进程"""
    print("\n🛑 正在关闭系统...")
    for process in processes:
        if process and process.poll() is None:
            process.terminate()
    
    time.sleep(2)
    
    for process in processes:
        if process and process.poll() is None:
            process.kill()
    
    print("✅ 系统已关闭")

def signal_handler(sig, frame):
    """信号处理"""
    cleanup()
    sys.exit(0)

def install_missing_packages():
    """安装缺失的包"""
    packages = ["streamlit", "fastapi", "uvicorn", "pandas", "requests", "plotly"]
    
    for package in packages:
        try:
            __import__(package)
        except ImportError:
            print(f"📦 安装 {package}...")
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         capture_output=True)

def create_backend():
    """创建后端"""
    backend_code = '''
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime

app = FastAPI(title="LLM系统API")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "LLM任务分配系统", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/api/v1/health")
async def api_health():
    return {"status": "healthy", "version": "1.0.0"}
'''
    
    Path("backend").mkdir(exist_ok=True)
    with open("backend/app.py", "w", encoding="utf-8") as f:
        f.write(backend_code)

def start_backend():
    """启动后端"""
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "backend.app:app", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        processes.append(process)
        return process
    except Exception as e:
        print(f"后端启动失败: {e}")
        return None

def start_frontend():
    """启动前端"""
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run",
            "frontend/main.py",
            "--server.port", "8501",
            "--server.headless", "true"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        processes.append(process)
        return process
    except Exception as e:
        print(f"前端启动失败: {e}")
        return None

def main():
    """主函数"""
    print("🚁 LLM任务分配系统")
    print("=" * 30)
    
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    
    # 安装依赖
    print("📦 检查依赖...")
    install_missing_packages()
    
    # 创建目录
    for dir_name in ["data", "backend", "logs"]:
        Path(dir_name).mkdir(exist_ok=True)
    
    # 创建后端
    print("🔧 创建后端...")
    create_backend()
    
    # 启动后端
    print("🚀 启动后端...")
    backend = start_backend()
    time.sleep(3)
    
    # 启动前端
    print("🚀 启动前端...")
    frontend = start_frontend()
    time.sleep(3)
    
    # 显示信息
    print("\n🎉 启动完成!")
    print("📍 前端: http://localhost:8501")
    print("📍 后端: http://localhost:8000")
    print("🔐 账号: admin / admin123")
    print("🛑 按 Ctrl+C 停止")
    
    # 保持运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        cleanup()

if __name__ == "__main__":
    main()
