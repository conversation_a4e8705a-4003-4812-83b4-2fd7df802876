from sqlmodel import SQLModel, Field, Relationship, JSON, Column
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class ScenarioStatus(str, Enum):
    """场景状态枚举"""
    DRAFT = "draft"
    ACTIVE = "active"
    ARCHIVED = "archived"


class FloatBuoyType(str, Enum):
    """浮漂类型枚举"""
    COMMUNICATION = "communication"
    NAVIGATION = "navigation"
    SURVEILLANCE = "surveillance"
    WEATHER = "weather"


class FloatBuoy(SQLModel):
    """浮漂模型"""
    id: str = Field(primary_key=True)
    name: str
    type: FloatBuoyType
    latitude: float = Field(ge=-90, le=90)
    longitude: float = Field(ge=-180, le=180)
    altitude: float = Field(default=0.0)
    frequency_range: List[float] = Field(default_factory=list)
    power_level: float = Field(ge=0, le=100, default=50.0)
    coverage_radius: float = Field(ge=0, default=1000.0)  # 覆盖半径（米）
    is_active: bool = Field(default=True)
    properties: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))


class EnvironmentParams(SQLModel):
    """环境参数模型"""
    wind_speed: float = Field(ge=0, le=50, default=5.0)  # 风速 m/s
    wind_direction: float = Field(ge=0, le=360, default=0.0)  # 风向 度
    wave_height: float = Field(ge=0, le=10, default=1.0)  # 浪高 m
    visibility: float = Field(ge=0, le=100, default=80.0)  # 能见度 km
    temperature: float = Field(ge=-50, le=50, default=20.0)  # 温度 °C
    humidity: float = Field(ge=0, le=100, default=60.0)  # 湿度 %
    sea_state: int = Field(ge=0, le=9, default=2)  # 海况等级
    salinity: float = Field(ge=0, le=50, default=35.0)  # 盐度 ppt


class ScenarioBase(SQLModel):
    """场景基础模型"""
    name: str = Field(min_length=1, max_length=100)
    description: Optional[str] = Field(default=None, max_length=500)
    status: ScenarioStatus = Field(default=ScenarioStatus.DRAFT)
    area_bounds: Dict[str, float] = Field(default_factory=dict, sa_column=Column(JSON))  # 区域边界
    environment: EnvironmentParams = Field(default_factory=EnvironmentParams, sa_column=Column(JSON))


class Scenario(ScenarioBase, table=True):
    """场景数据库模型"""
    id: Optional[int] = Field(default=None, primary_key=True)
    owner_id: int = Field(foreign_key="user.id")
    float_buoys: List[FloatBuoy] = Field(default_factory=list, sa_column=Column(JSON))
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    thumbnail_path: Optional[str] = Field(default=None)  # 缩略图路径
    
    # 关联关系
    owner: "User" = Relationship(back_populates="scenarios")
    tasks: List["Task"] = Relationship(back_populates="scenario")


class ScenarioCreate(ScenarioBase):
    """创建场景模型"""
    float_buoys: List[FloatBuoy] = Field(default_factory=list)


class ScenarioUpdate(SQLModel):
    """更新场景模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=100)
    description: Optional[str] = Field(default=None, max_length=500)
    status: Optional[ScenarioStatus] = Field(default=None)
    area_bounds: Optional[Dict[str, float]] = Field(default=None)
    environment: Optional[EnvironmentParams] = Field(default=None)
    float_buoys: Optional[List[FloatBuoy]] = Field(default=None)


class ScenarioRead(ScenarioBase):
    """读取场景模型"""
    id: int
    owner_id: int
    float_buoys: List[FloatBuoy]
    created_at: datetime
    updated_at: Optional[datetime]
    thumbnail_path: Optional[str]


class ScenarioSummary(SQLModel):
    """场景摘要模型"""
    id: int
    name: str
    description: Optional[str]
    status: ScenarioStatus
    buoy_count: int
    created_at: datetime
    thumbnail_path: Optional[str]


# 导入其他模型以避免循环导入
from backend.models.user import User
from backend.models.task import Task
