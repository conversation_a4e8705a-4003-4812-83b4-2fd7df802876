# 基础Web框架 - 核心必需
fastapi
uvicorn
streamlit

# 数据处理 - 基础必需
pandas
numpy
requests

# 可视化 - 基础必需
plotly

# 系统工具 - 必需
psutil
python-dotenv

# 安全认证 - 必需
passlib[bcrypt]
python-multipart

# 高级数据库ORM - 必需
sqlmodel
pydantic
pydantic-settings

# HTTP客户端 - 必需
httpx

# 数据库（使用SQLite，无需额外安装）
# sqlite3 是Python内置模块

# 可选包 - 根据需要安装
# 如果需要完整功能，请取消注释以下包：

# 高级数据库ORM
# sqlmodel
# pydantic
# pydantic-settings

# HTTP客户端
# httpx

# 机器学习
# torch --index-url https://download.pytorch.org/whl/cpu
# transformers
# scikit-learn

# 高级可视化
# matplotlib
# seaborn

# 异步和缓存
# aiofiles
# redis
# aioredis

# 任务队列
# celery

# 加密和安全
python-jose[cryptography]
cryptography

# 日志
# loguru

# 开发工具
# pytest
# black
# flake8
