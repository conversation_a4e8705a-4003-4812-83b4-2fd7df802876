import streamlit as st
import json
import sqlite3
import zipfile
import shutil
from pathlib import Path
from datetime import datetime
import pandas as pd
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from frontend.utils.logger import log_operation


def create_backup():
    """创建系统备份"""
    try:
        backup_dir = Path("data/backups")
        backup_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = backup_dir / f"system_backup_{timestamp}.zip"
        
        with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 备份数据库文件
            for db_file in Path("data").glob("*.db"):
                if db_file.exists():
                    zipf.write(db_file, f"databases/{db_file.name}")
            
            # 备份配置文件
            config_files = ["data/model_config.json"]
            for config_file in config_files:
                config_path = Path(config_file)
                if config_path.exists():
                    zipf.write(config_path, f"configs/{config_path.name}")
            
            # 备份场景文件
            scenarios_dir = Path("data/scenarios")
            if scenarios_dir.exists():
                for scenario_file in scenarios_dir.glob("*.json"):
                    zipf.write(scenario_file, f"scenarios/{scenario_file.name}")
            
            # 备份上传文件
            uploads_dir = Path("data/uploads")
            if uploads_dir.exists():
                for upload_file in uploads_dir.rglob("*"):
                    if upload_file.is_file():
                        rel_path = upload_file.relative_to(uploads_dir)
                        zipf.write(upload_file, f"uploads/{rel_path}")
        
        return backup_file
    except Exception as e:
        st.error(f"备份创建失败: {e}")
        return None


def restore_backup(backup_file):
    """恢复系统备份"""
    try:
        with zipfile.ZipFile(backup_file, 'r') as zipf:
            # 创建临时恢复目录
            restore_dir = Path("data/restore_temp")
            restore_dir.mkdir(exist_ok=True)
            
            # 解压备份文件
            zipf.extractall(restore_dir)
            
            # 恢复数据库文件
            db_dir = restore_dir / "databases"
            if db_dir.exists():
                for db_file in db_dir.glob("*.db"):
                    target_path = Path("data") / db_file.name
                    shutil.copy2(db_file, target_path)
            
            # 恢复配置文件
            config_dir = restore_dir / "configs"
            if config_dir.exists():
                for config_file in config_dir.glob("*.json"):
                    target_path = Path("data") / config_file.name
                    shutil.copy2(config_file, target_path)
            
            # 恢复场景文件
            scenarios_dir = restore_dir / "scenarios"
            if scenarios_dir.exists():
                target_scenarios_dir = Path("data/scenarios")
                target_scenarios_dir.mkdir(exist_ok=True)
                for scenario_file in scenarios_dir.glob("*.json"):
                    target_path = target_scenarios_dir / scenario_file.name
                    shutil.copy2(scenario_file, target_path)
            
            # 恢复上传文件
            uploads_dir = restore_dir / "uploads"
            if uploads_dir.exists():
                target_uploads_dir = Path("data/uploads")
                target_uploads_dir.mkdir(exist_ok=True)
                shutil.copytree(uploads_dir, target_uploads_dir, dirs_exist_ok=True)
            
            # 清理临时目录
            shutil.rmtree(restore_dir)
            
        return True
    except Exception as e:
        st.error(f"备份恢复失败: {e}")
        return False


def export_data(data_type):
    """导出数据"""
    try:
        if data_type == "users":
            return export_users_data()
        elif data_type == "scenarios":
            return export_scenarios_data()
        elif data_type == "logs":
            return export_logs_data()
        else:
            return None
    except Exception as e:
        st.error(f"数据导出失败: {e}")
        return None


def export_users_data():
    """导出用户数据"""
    db_path = Path("data/users.db")
    if not db_path.exists():
        return None
    
    conn = sqlite3.connect(db_path)
    df = pd.read_sql_query("SELECT id, username, email, role, created_at FROM users", conn)
    conn.close()
    
    return df.to_csv(index=False)


def export_scenarios_data():
    """导出场景数据"""
    scenarios_dir = Path("data/scenarios")
    if not scenarios_dir.exists():
        return None
    
    scenarios = []
    for scenario_file in scenarios_dir.glob("*.json"):
        with open(scenario_file, 'r', encoding='utf-8') as f:
            scenarios.append(json.load(f))
    
    if scenarios:
        df = pd.json_normalize(scenarios)
        return df.to_csv(index=False)
    return None


def export_logs_data():
    """导出日志数据"""
    db_path = Path("data/system_logs.db")
    if not db_path.exists():
        return None
    
    conn = sqlite3.connect(db_path)
    
    # 导出操作日志
    operation_logs = pd.read_sql_query("SELECT * FROM operation_logs ORDER BY timestamp DESC", conn)
    
    # 导出系统日志
    system_logs = pd.read_sql_query("SELECT * FROM system_logs ORDER BY timestamp DESC", conn)
    
    conn.close()
    
    # 合并数据
    combined_data = {
        'operation_logs': operation_logs.to_dict('records'),
        'system_logs': system_logs.to_dict('records')
    }
    
    return json.dumps(combined_data, ensure_ascii=False, indent=2)


def show():
    """显示数据管理页面"""
    if not st.session_state.get('authenticated', False):
        st.error("请先登录")
        return
    
    user = st.session_state.get('user', {})
    is_admin = user.get('role') == 'admin'
    
    if not is_admin:
        st.error("⚠️ 只有管理员可以访问数据管理功能")
        return
    
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
    ">
        <h2 style="margin: 0;">💾 数据管理</h2>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">系统数据备份、恢复和导出管理</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 功能选项卡
    tab1, tab2, tab3 = st.tabs(["🔄 备份与恢复", "📤 数据导出", "🗑️ 数据清理"])
    
    with tab1:
        show_backup_restore()
    
    with tab2:
        show_data_export()
    
    with tab3:
        show_data_cleanup()


def show_backup_restore():
    """显示备份恢复功能"""
    st.markdown("### 🔄 系统备份与恢复")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 📦 创建备份")
        st.info("备份将包含所有用户数据、配置文件、场景数据和日志")
        
        if st.button("🔄 创建系统备份", type="primary", use_container_width=True, key="create_system_backup"):
            with st.spinner("正在创建备份..."):
                backup_file = create_backup()
                if backup_file:
                    log_operation("创建系统备份", "数据管理", {"backup_file": str(backup_file)})
                    st.success(f"✅ 备份创建成功: {backup_file.name}")
                    
                    # 提供下载链接
                    with open(backup_file, 'rb') as f:
                        st.download_button(
                            label="📥 下载备份文件",
                            data=f.read(),
                            file_name=backup_file.name,
                            mime="application/zip"
                        )
    
    with col2:
        st.markdown("#### 📥 恢复备份")
        st.warning("⚠️ 恢复备份将覆盖现有数据，请谨慎操作")
        
        uploaded_file = st.file_uploader("选择备份文件", type=['zip'])
        
        if uploaded_file is not None:
            if st.button("🔄 恢复备份", type="secondary", use_container_width=True, key="restore_system_backup"):
                # 保存上传的文件
                temp_file = Path(f"data/temp_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip")
                with open(temp_file, 'wb') as f:
                    f.write(uploaded_file.getvalue())
                
                with st.spinner("正在恢复备份..."):
                    if restore_backup(temp_file):
                        log_operation("恢复系统备份", "数据管理", {"backup_file": uploaded_file.name})
                        st.success("✅ 备份恢复成功！")
                        st.info("🔄 请刷新页面以查看恢复的数据")
                    
                # 清理临时文件
                if temp_file.exists():
                    temp_file.unlink()
    
    # 显示现有备份文件
    st.markdown("#### 📋 现有备份文件")
    backup_dir = Path("data/backups")
    if backup_dir.exists():
        backup_files = list(backup_dir.glob("*.zip"))
        if backup_files:
            for backup_file in sorted(backup_files, reverse=True):
                col1, col2, col3 = st.columns([3, 1, 1])
                with col1:
                    st.text(backup_file.name)
                with col2:
                    file_size = backup_file.stat().st_size / 1024 / 1024
                    st.text(f"{file_size:.1f} MB")
                with col3:
                    if st.button("🗑️", key=f"delete_{backup_file.name}"):
                        backup_file.unlink()
                        st.rerun()
        else:
            st.info("📝 暂无备份文件")
    else:
        st.info("📝 备份目录不存在")


def show_data_export():
    """显示数据导出功能"""
    st.markdown("### 📤 数据导出")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("#### 👥 用户数据")
        if st.button("导出用户数据", use_container_width=True, key="export_users_data"):
            csv_data = export_data("users")
            if csv_data:
                log_operation("导出用户数据", "数据管理")
                st.download_button(
                    label="📥 下载用户数据",
                    data=csv_data,
                    file_name=f"users_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv",
                    key="download_users_data"
                )
            else:
                st.error("❌ 用户数据导出失败")

    with col2:
        st.markdown("#### 🗺️ 场景数据")
        if st.button("导出场景数据", use_container_width=True, key="export_scenarios_data"):
            csv_data = export_data("scenarios")
            if csv_data:
                log_operation("导出场景数据", "数据管理")
                st.download_button(
                    label="📥 下载场景数据",
                    data=csv_data,
                    file_name=f"scenarios_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv",
                    key="download_scenarios_data"
                )
            else:
                st.error("❌ 场景数据导出失败")

    with col3:
        st.markdown("#### 📋 日志数据")
        if st.button("导出日志数据", use_container_width=True, key="export_logs_data"):
            json_data = export_data("logs")
            if json_data:
                log_operation("导出日志数据", "数据管理")
                st.download_button(
                    label="📥 下载日志数据",
                    data=json_data,
                    file_name=f"logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json",
                    key="download_logs_data"
                )
            else:
                st.error("❌ 日志数据导出失败")


def show_data_cleanup():
    """显示数据清理功能"""
    st.markdown("### 🗑️ 数据清理")
    
    st.warning("⚠️ 数据清理操作不可逆，请谨慎操作")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 📋 清理日志")
        days = st.number_input("保留天数", min_value=1, max_value=365, value=30, key="log_retention_days")

        if st.button("🗑️ 清理旧日志", type="secondary", key="cleanup_old_logs"):
            from frontend.utils.logger import clear_old_logs
            if clear_old_logs(days):
                log_operation("清理系统日志", "数据管理", {"days": days})
                st.success(f"✅ 已清理 {days} 天前的日志")
            else:
                st.error("❌ 日志清理失败")

    with col2:
        st.markdown("#### 📁 清理临时文件")

        if st.button("🗑️ 清理临时文件", type="secondary", key="cleanup_temp_files"):
            temp_files_cleaned = 0

            # 清理上传临时文件
            uploads_dir = Path("data/uploads")
            if uploads_dir.exists():
                for temp_file in uploads_dir.glob("temp_*"):
                    temp_file.unlink()
                    temp_files_cleaned += 1

            # 清理其他临时文件
            for temp_file in Path("data").glob("temp_*"):
                temp_file.unlink()
                temp_files_cleaned += 1

            log_operation("清理临时文件", "数据管理", {"files_cleaned": temp_files_cleaned})
            st.success(f"✅ 已清理 {temp_files_cleaned} 个临时文件")
