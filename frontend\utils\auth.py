import streamlit as st
import requests
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class AuthManager:
    """认证管理器"""
    
    def __init__(self, api_client):
        self.api_client = api_client
        self.token_key = "access_token"
        self.user_key = "user_info"
    
    def login(self, username: str, password: str) -> bool:
        """用户登录"""
        try:
            response = self.api_client.post("/auth/login-json", {
                "username": username,
                "password": password
            })
            
            if response and response.get("access_token"):
                # 保存令牌和用户信息
                st.session_state[self.token_key] = response["access_token"]
                st.session_state[self.user_key] = response["user"]
                st.session_state.authenticated = True
                
                # 设置API客户端的认证头
                self.api_client.set_auth_token(response["access_token"])
                
                logger.info(f"User {username} logged in successfully")
                return True
            
        except Exception as e:
            logger.error(f"Lo<PERSON> failed for user {username}: {e}")
            st.error(f"登录失败: {str(e)}")
        
        return False
    
    def register(self, user_data: Dict[str, Any]) -> bool:
        """用户注册"""
        try:
            response = self.api_client.post("/auth/register", user_data)
            
            if response:
                st.success("注册成功！请登录。")
                logger.info(f"User {user_data['username']} registered successfully")
                return True
            
        except Exception as e:
            logger.error(f"Registration failed for user {user_data.get('username')}: {e}")
            st.error(f"注册失败: {str(e)}")
        
        return False
    
    def logout(self):
        """用户登出"""
        try:
            # 调用后端登出接口
            if self.is_authenticated():
                self.api_client.post("/auth/logout")
            
            # 清除本地会话状态
            if self.token_key in st.session_state:
                del st.session_state[self.token_key]
            if self.user_key in st.session_state:
                del st.session_state[self.user_key]
            if "authenticated" in st.session_state:
                del st.session_state["authenticated"]
            
            # 清除API客户端的认证头
            self.api_client.clear_auth_token()
            
            logger.info("User logged out successfully")
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
    
    def is_authenticated(self) -> bool:
        """检查用户是否已认证"""
        return (
            st.session_state.get("authenticated", False) and
            self.token_key in st.session_state and
            self.user_key in st.session_state
        )
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        if self.is_authenticated():
            return st.session_state.get(self.user_key)
        return None
    
    def get_access_token(self) -> Optional[str]:
        """获取访问令牌"""
        if self.is_authenticated():
            return st.session_state.get(self.token_key)
        return None
    
    def refresh_token(self) -> bool:
        """刷新访问令牌"""
        try:
            if not self.is_authenticated():
                return False
            
            response = self.api_client.post("/auth/refresh")
            
            if response and response.get("access_token"):
                # 更新令牌
                st.session_state[self.token_key] = response["access_token"]
                st.session_state[self.user_key] = response["user"]
                
                # 更新API客户端的认证头
                self.api_client.set_auth_token(response["access_token"])
                
                logger.info("Token refreshed successfully")
                return True
            
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            # 令牌刷新失败，清除认证状态
            self.logout()
        
        return False
    
    def verify_token(self) -> bool:
        """验证令牌有效性"""
        try:
            if not self.is_authenticated():
                return False
            
            response = self.api_client.get("/auth/verify-token")
            
            if response and response.get("valid"):
                return True
            else:
                # 令牌无效，清除认证状态
                self.logout()
                return False
            
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            # 验证失败，清除认证状态
            self.logout()
            return False
    
    def update_user_profile(self, user_data: Dict[str, Any]) -> bool:
        """更新用户资料"""
        try:
            response = self.api_client.put("/users/me", user_data)
            
            if response:
                # 更新本地用户信息
                st.session_state[self.user_key] = response
                st.success("用户资料更新成功！")
                logger.info("User profile updated successfully")
                return True
            
        except Exception as e:
            logger.error(f"Profile update failed: {e}")
            st.error(f"更新失败: {str(e)}")
        
        return False
    
    def check_permission(self, required_role: str = "user") -> bool:
        """检查用户权限"""
        user = self.get_current_user()
        if not user:
            return False
        
        user_role = user.get("role", "user")
        
        # 角色层级：admin > user > viewer
        role_hierarchy = {"admin": 3, "user": 2, "viewer": 1}
        
        user_level = role_hierarchy.get(user_role, 0)
        required_level = role_hierarchy.get(required_role, 0)
        
        return user_level >= required_level
    
    def require_auth(self, func):
        """装饰器：要求用户认证"""
        def wrapper(*args, **kwargs):
            if not self.is_authenticated():
                st.error("请先登录！")
                st.stop()
            return func(*args, **kwargs)
        return wrapper
    
    def require_role(self, required_role: str):
        """装饰器：要求特定角色"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                if not self.check_permission(required_role):
                    st.error(f"权限不足！需要 {required_role} 角色。")
                    st.stop()
                return func(*args, **kwargs)
            return wrapper
        return decorator
