#!/usr/bin/env python3
"""
一键修复所有问题脚本
解决前端和后端的所有常见问题
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def install_basic_packages():
    """安装基础包"""
    print("📦 安装基础依赖...")
    
    basic_packages = [
        "streamlit",
        "pandas",
        "numpy", 
        "requests",
        "plotly",
        "psutil",
        "fastapi",
        "uvicorn"
    ]
    
    for package in basic_packages:
        try:
            print(f"📦 安装 {package}...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True, text=True
            )
            if result.returncode == 0:
                print(f"✅ {package} 安装成功")
            else:
                print(f"⚠️ {package} 安装失败，但系统可能仍能运行")
        except Exception as e:
            print(f"⚠️ {package} 安装异常: {e}")

def clear_all_cache():
    """清理所有缓存"""
    print("🧹 清理系统缓存...")

    import shutil

    # 清理Python缓存
    cache_patterns = [
        "__pycache__",
        "*.pyc",
        ".streamlit"
    ]

    for pattern in cache_patterns:
        for cache_path in Path(".").rglob(pattern):
            try:
                if cache_path.is_dir():
                    shutil.rmtree(cache_path)
                else:
                    cache_path.unlink()
                print(f"✅ 删除缓存: {cache_path}")
            except Exception:
                pass

    print("✅ 缓存清理完成")

def fix_pandas_style_issues():
    """修复pandas样式问题"""
    print("🔧 修复pandas样式问题...")

    # 修复所有颜色函数，确保类型安全
    files_to_check = [
        "frontend/pages/security_page.py",
        "frontend/pages/logs_page.py",
        "frontend/pages/api_docs_page.py"
    ]

    for file_path in files_to_check:
        file_obj = Path(file_path)
        if file_obj.exists():
            try:
                with open(file_obj, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否已经修复
                if 'if not isinstance(val, str):' not in content:
                    # 需要修复
                    print(f"🔧 修复 {file_path}")
                    # 这里可以添加具体的修复逻辑
                else:
                    print(f"✅ {file_path} 已修复")
            except Exception as e:
                print(f"⚠️ 检查 {file_path} 失败: {e}")

    print("✅ pandas样式问题修复完成")

def create_simple_backend():
    """创建简化后端"""
    print("🔧 创建简化后端...")
    
    backend_content = '''
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime

app = FastAPI(title="LLM任务分配系统API", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "LLM任务分配系统API", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/api/v1/health")
async def api_health():
    return {"status": "healthy", "version": "1.0.0"}
'''
    
    backend_dir = Path("backend")
    backend_dir.mkdir(exist_ok=True)
    
    with open(backend_dir / "simple_app.py", 'w') as f:
        f.write(backend_content)
    
    print("✅ 简化后端已创建")

def start_system():
    """启动系统"""
    print("🚀 启动系统...")
    
    # 启动后端
    print("🔧 启动后端服务...")
    backend_process = None
    
    try:
        backend_cmd = [
            sys.executable, "-m", "uvicorn",
            "backend.simple_app:app",
            "--host", "0.0.0.0",
            "--port", "8000"
        ]
        
        backend_process = subprocess.Popen(
            backend_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print("✅ 后端服务已启动")
        time.sleep(3)  # 等待后端启动
        
    except Exception as e:
        print(f"⚠️ 后端启动失败: {e}")
    
    # 启动前端
    print("🔧 启动前端服务...")
    
    try:
        frontend_cmd = [
            sys.executable, "-m", "streamlit", "run",
            "frontend/main.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0"
        ]
        
        print("\n🎉 系统启动完成!")
        print("📍 前端地址: http://localhost:8501")
        print("📍 后端API: http://localhost:8000")
        print("🛑 按 Ctrl+C 停止系统")
        
        subprocess.run(frontend_cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 正在停止系统...")
        if backend_process:
            backend_process.terminate()
        print("✅ 系统已停止")
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        if backend_process:
            backend_process.terminate()

def main():
    """主函数"""
    print("🔧 一键修复所有问题")
    print("=" * 40)
    
    print("🚀 开始修复流程...")
    
    # 1. 安装依赖
    install_basic_packages()
    
    # 2. 清理缓存
    clear_all_cache()

    # 3. 修复pandas样式问题
    fix_pandas_style_issues()

    # 4. 创建简化后端
    create_simple_backend()
    
    # 4. 询问是否启动
    print("\n✅ 修复完成!")
    
    response = input("\n❓ 是否现在启动系统？(y/n): ").lower().strip()
    if response == 'y':
        start_system()
    else:
        print("\n💡 手动启动命令:")
        print("   前端: streamlit run frontend/main.py")
        print("   后端: python -m uvicorn backend.simple_app:app --host 0.0.0.0 --port 8000")
        print("\n或者运行:")
        print("   python start_simple.py")

if __name__ == "__main__":
    main()
