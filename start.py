
"""
LLM任务分配系统启动脚本
"""

import os
import sys
import subprocess
import time
import signal
from pathlib import Path
import argparse
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
os.chdir(PROJECT_ROOT)

# 添加项目路径到Python路径
sys.path.insert(0, str(PROJECT_ROOT))


class SystemManager:
    """系统管理器"""
    
    def __init__(self):
        self.processes = {}
        self.running = False
    
    def start_backend(self):
        """启动后端服务"""
        logger.info("启动后端服务...")
        
        # 设置环境变量
        env = os.environ.copy()
        env.update({
            'PYTHONPATH': str(PROJECT_ROOT),
            'DATABASE_URL': 'sqlite:///./data/app.db',
            'REDIS_URL': 'redis://localhost:6379/0',
            'SECRET_KEY': 'dev-secret-key-change-in-production',
            'ENVIRONMENT': 'development',
            'DEBUG': 'True'
        })
        
        # 启动FastAPI服务
        cmd = [
            sys.executable, "-m", "uvicorn",
            "backend.app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        process = subprocess.Popen(
            cmd,
            env=env,
            cwd=PROJECT_ROOT,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        self.processes['backend'] = process
        logger.info("后端服务已启动 (PID: %d)", process.pid)
        
        return process
    
    def start_frontend(self):
        """启动前端服务"""
        logger.info("启动前端服务...")
        
        # 设置环境变量
        env = os.environ.copy()
        env.update({
            'PYTHONPATH': str(PROJECT_ROOT),
            'BACKEND_URL': 'http://localhost:8000'
        })
        
        # 启动Streamlit服务
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            "frontend/main.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0",
            "--server.headless", "true"
        ]
        
        process = subprocess.Popen(
            cmd,
            env=env,
            cwd=PROJECT_ROOT,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        self.processes['frontend'] = process
        logger.info("前端服务已启动 (PID: %d)", process.pid)
        
        return process
    
    def check_dependencies(self):
        """检查系统依赖"""
        logger.info("检查系统依赖...")

        required_packages = {
            'streamlit': '前端框架',
            'pandas': '数据处理',
            'numpy': '数值计算',
            'requests': 'HTTP客户端',
            'plotly': '数据可视化'
        }

        optional_packages = {
            'fastapi': '后端API框架',
            'uvicorn': 'ASGI服务器',
            'psutil': '系统监控',
            'passlib': '密码加密'
        }

        missing_required = []
        missing_optional = []

        # 检查必需包
        for package, description in required_packages.items():
            try:
                __import__(package)
                logger.info(f"✅ {package} ({description}) 可用")
            except ImportError:
                logger.error(f"❌ {package} ({description}) 缺失")
                missing_required.append(package)

        # 检查可选包
        for package, description in optional_packages.items():
            try:
                __import__(package)
                logger.info(f"✅ {package} ({description}) 可用")
            except ImportError:
                logger.warning(f"⚠️ {package} ({description}) 缺失")
                missing_optional.append(package)

        if missing_required:
            logger.error(f"缺少必需依赖: {', '.join(missing_required)}")
            logger.error("请运行: python install_dependencies.py")
            return False

        if missing_optional:
            logger.warning(f"缺少可选依赖: {', '.join(missing_optional)}")
            logger.warning("某些高级功能可能不可用")

        logger.info("依赖检查完成")
        return True
    
    def setup_directories(self):
        """设置目录结构"""
        logger.info("设置目录结构...")
        
        directories = [
            'data',
            'logs',
            'models',
            'static',
            'data/uploads'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        logger.info("目录结构设置完成")
    
    def start_all(self):
        """启动所有服务"""
        logger.info("启动LLM任务分配系统...")
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 设置目录
        self.setup_directories()
        
        try:
            # 启动后端
            self.start_backend()
            time.sleep(3)  # 等待后端启动
            
            # 启动前端
            self.start_frontend()
            time.sleep(2)  # 等待前端启动
            
            self.running = True
            
            logger.info("系统启动完成!")
            logger.info("前端地址: http://localhost:8501")
            logger.info("后端API: http://localhost:8000")
            logger.info("API文档: http://localhost:8000/docs")
            logger.info("按 Ctrl+C 停止系统")
            
            return True
            
        except Exception as e:
            logger.error("启动失败: %s", e)
            self.stop_all()
            return False
    
    def stop_all(self):
        """停止所有服务"""
        logger.info("停止系统服务...")
        
        for name, process in self.processes.items():
            if process and process.poll() is None:
                logger.info("停止 %s 服务 (PID: %d)", name, process.pid)
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning("强制终止 %s 服务", name)
                    process.kill()
                except Exception as e:
                    logger.error("停止 %s 服务时出错: %s", name, e)
        
        self.processes.clear()
        self.running = False
        logger.info("系统已停止")
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            try:
                for name, process in list(self.processes.items()):
                    if process.poll() is not None:
                        logger.error("%s 服务意外退出 (退出码: %d)", name, process.returncode)
                        self.running = False
                        break
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                logger.info("收到停止信号")
                self.running = False
                break
            except Exception as e:
                logger.error("监控进程时出错: %s", e)
                break
        
        self.stop_all()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='LLM任务分配系统启动脚本')
    parser.add_argument('--backend-only', action='store_true', help='仅启动后端服务')
    parser.add_argument('--frontend-only', action='store_true', help='仅启动前端服务')
    parser.add_argument('--check-deps', action='store_true', help='仅检查依赖')
    
    args = parser.parse_args()
    
    manager = SystemManager()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        logger.info("收到信号 %d，正在停止系统...", signum)
        manager.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        if args.check_deps:
            # 仅检查依赖
            if manager.check_dependencies():
                logger.info("所有依赖都已满足")
                sys.exit(0)
            else:
                sys.exit(1)
        
        elif args.backend_only:
            # 仅启动后端
            if manager.check_dependencies():
                manager.setup_directories()
                manager.start_backend()
                manager.monitor_processes()
        
        elif args.frontend_only:
            # 仅启动前端
            if manager.check_dependencies():
                manager.start_frontend()
                manager.monitor_processes()
        
        else:
            # 启动完整系统
            if manager.start_all():
                manager.monitor_processes()
    
    except Exception as e:
        logger.error("系统启动失败: %s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()
