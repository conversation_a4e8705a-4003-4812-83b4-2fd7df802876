#!/usr/bin/env python3
"""
Streamlit重启脚本
解决Streamlit元素ID冲突问题
"""

import os
import sys
import subprocess
import time
import shutil
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
os.chdir(PROJECT_ROOT)

def clear_streamlit_cache():
    """清理Streamlit缓存"""
    print("🧹 清理Streamlit缓存...")
    
    try:
        # 清理用户目录下的Streamlit缓存
        streamlit_cache_dir = Path.home() / ".streamlit"
        if streamlit_cache_dir.exists():
            cache_files = list(streamlit_cache_dir.rglob("*.cache"))
            for cache_file in cache_files:
                try:
                    cache_file.unlink()
                    print(f"✅ 删除缓存: {cache_file.name}")
                except Exception:
                    pass
        
        # 清理项目目录下的缓存
        project_cache_dirs = [
            ".streamlit",
            "__pycache__",
            "frontend/__pycache__",
            "frontend/pages/__pycache__",
            "frontend/utils/__pycache__"
        ]
        
        for cache_dir in project_cache_dirs:
            cache_path = Path(cache_dir)
            if cache_path.exists():
                try:
                    if cache_path.is_dir():
                        shutil.rmtree(cache_path)
                    else:
                        cache_path.unlink()
                    print(f"✅ 删除缓存目录: {cache_dir}")
                except Exception:
                    pass
        
        print("✅ Streamlit缓存清理完成")
        return True
    except Exception as e:
        print(f"⚠️ 清理缓存失败: {e}")
        return False

def kill_streamlit_processes():
    """终止Streamlit进程"""
    print("🔄 查找并终止Streamlit进程...")
    
    try:
        if os.name == 'nt':  # Windows
            # 查找Streamlit进程
            result = subprocess.run(
                ['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'],
                capture_output=True, text=True
            )
            
            if 'streamlit' in result.stdout.lower():
                print("⚠️ 发现Streamlit进程，尝试终止...")
                subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                             capture_output=True)
                time.sleep(2)
                print("✅ 进程终止完成")
            else:
                print("✅ 没有发现Streamlit进程")
        else:  # Linux/Mac
            subprocess.run(['pkill', '-f', 'streamlit'], capture_output=True)
            time.sleep(2)
            print("✅ 进程终止完成")
            
        return True
    except Exception as e:
        print(f"⚠️ 终止进程失败: {e}")
        return False

def create_streamlit_config():
    """创建Streamlit配置"""
    print("⚙️ 创建Streamlit配置...")
    
    try:
        # 创建项目级配置目录
        config_dir = Path(".streamlit")
        config_dir.mkdir(exist_ok=True)
        
        # 创建配置文件
        config_file = config_dir / "config.toml"
        
        config_content = """
[server]
headless = true
enableCORS = false
enableXsrfProtection = false
maxUploadSize = 200

[browser]
gatherUsageStats = false

[theme]
base = "light"
primaryColor = "#667eea"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"

[client]
caching = false
"""
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"✅ 配置文件已创建: {config_file}")
        return True
    except Exception as e:
        print(f"⚠️ 创建配置失败: {e}")
        return False

def start_streamlit_clean():
    """干净启动Streamlit"""
    print("🚀 启动Streamlit...")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONPATH': str(PROJECT_ROOT),
        'STREAMLIT_SERVER_HEADLESS': 'true',
        'STREAMLIT_BROWSER_GATHER_USAGE_STATS': 'false'
    })
    
    # 启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        "frontend/main.py",
        "--server.port", "8501",
        "--server.address", "0.0.0.0",
        "--server.headless", "true",
        "--browser.gatherUsageStats", "false"
    ]
    
    try:
        print("📍 前端地址: http://localhost:8501")
        print("💡 提示: 如果仍有问题，请刷新浏览器页面")
        print("🛑 按 Ctrl+C 停止服务")
        
        subprocess.run(cmd, env=env, cwd=PROJECT_ROOT)
        
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🔄 Streamlit重启脚本")
    print("=" * 40)
    
    print("\n🚀 开始重启流程...")
    
    # 1. 终止现有进程
    kill_streamlit_processes()
    
    # 2. 清理缓存
    clear_streamlit_cache()
    
    # 3. 创建配置
    create_streamlit_config()
    
    # 4. 等待一下
    print("⏳ 等待系统稳定...")
    time.sleep(3)
    
    # 5. 重新启动
    print("\n🎉 准备重新启动Streamlit...")
    
    response = input("❓ 是否现在启动？(y/n): ").lower().strip()
    if response == 'y':
        start_streamlit_clean()
    else:
        print("💡 手动启动命令:")
        print("   streamlit run frontend/main.py")
        print("   或")
        print("   python start_simple.py")

if __name__ == "__main__":
    main()
