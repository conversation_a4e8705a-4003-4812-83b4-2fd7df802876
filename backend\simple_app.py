
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import uvicorn

app = FastAPI(
    title="LLM任务分配系统API",
    description="无人机干扰决策系统后端API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "LLM任务分配系统API",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "backend",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/health")
async def api_health():
    return {
        "status": "healthy",
        "version": "1.0.0",
        "api": "v1",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/status")
async def system_status():
    return {
        "backend": "online",
        "database": "simulated",
        "llm": "not_configured",
        "frontend": "ready",
        "timestamp": datetime.now().isoformat()
    }

# 模拟用户认证
@app.post("/api/v1/auth/login")
async def login(credentials: dict = None):
    return {
        "access_token": "demo_token_" + datetime.now().strftime("%Y%m%d%H%M%S"),
        "token_type": "bearer",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "role": "admin"
        }
    }

@app.post("/api/v1/auth/register")
async def register(user_data: dict = None):
    return {
        "message": "用户注册成功",
        "user": {
            "id": 2,
            "username": user_data.get("username", "user"),
            "email": user_data.get("email", "<EMAIL>"),
            "role": "user"
        }
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
