import streamlit as st
import requests
import json
from pathlib import Path


def save_model_config(config):
    """保存模型配置到文件"""
    config_path = Path("data/model_config.json")
    config_path.parent.mkdir(exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)


def load_model_config():
    """加载模型配置"""
    config_path = Path("data/model_config.json")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    # 默认配置
    return {
        "local_model": {
            "enabled": False,
            "api_url": "http://26.99.66.138:1234",
            "model_name": "local-model",
            "max_tokens": 2048,
            "temperature": 0.7
        },
        "remote_model": {
            "enabled": False,
            "api_url": "",
            "api_key": "",
            "model_name": "",
            "max_tokens": 2048,
            "temperature": 0.7
        }
    }


def validate_config_inputs(api_url, api_key=None, model_name=None):
    """验证配置输入的有效性"""
    errors = []

    if not api_url or not api_url.strip():
        errors.append("API地址不能为空")

    if api_key and any(ord(char) > 127 for char in api_key):
        errors.append("API密钥包含非ASCII字符，请检查是否正确")

    if model_name and any(ord(char) > 127 for char in model_name if char not in ['/', '-', '_']):
        errors.append("模型名称包含特殊字符，请检查是否正确")

    return errors


def test_model_connection(api_url, api_key=None, model_name=None):
    """测试模型连接"""
    try:
        # 验证输入
        validation_errors = validate_config_inputs(api_url, api_key, model_name)
        if validation_errors:
            return False, "配置验证失败: " + "; ".join(validation_errors)

        # 确保所有字符串都是UTF-8编码
        if api_key:
            api_key = str(api_key).strip()
        if model_name:
            model_name = str(model_name).strip()
        if api_url:
            api_url = str(api_url).strip()

        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Accept": "application/json"
        }
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"

        # 测试请求
        test_data = {
            "model": model_name or "test-model",
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10,
            "temperature": 0.7
        }

        # 发送请求
        response = requests.post(
            f"{api_url}/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=10
        )

        # 更详细的错误信息
        if response.status_code == 200:
            return True, "连接成功"
        else:
            try:
                # 确保响应文本使用UTF-8解码
                response.encoding = 'utf-8'
                error_detail = response.json()
                return False, f"HTTP {response.status_code}: {error_detail}"
            except:
                response.encoding = 'utf-8'
                return False, f"HTTP {response.status_code}: {response.text}"
    except UnicodeEncodeError as e:
        return False, f"字符编码错误: {str(e)} - 请检查API密钥或模型名称是否包含特殊字符"
    except Exception as e:
        return False, str(e)


def show():
    """显示模型配置页面"""
    
    # 页面样式
    st.markdown("""
    <style>
        .config-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
            border-radius: 15px;
            color: white;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }
        
        .config-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            margin-bottom: 1.5rem;
        }
        
        .status-success {
            background: #dcfce7;
            color: #166534;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: 1px solid #bbf7d0;
        }
        
        .status-error {
            background: #fee2e2;
            color: #991b1b;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: 1px solid #fecaca;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #92400e;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: 1px solid #fde68a;
        }
    </style>
    """, unsafe_allow_html=True)

    # 简化的说明
    st.info("💡 请配置至少一个大语言模型API接口以使用AI规划功能")

    # 加载当前配置
    config = load_model_config()
    
    # 本地模型配置
    st.markdown('<div class="config-card">', unsafe_allow_html=True)
    st.markdown("### 🏠 本地模型配置")
    
    with st.form("local_model_form"):
        local_enabled = st.checkbox("启用本地模型", value=config["local_model"]["enabled"])
        
        col1, col2 = st.columns(2)
        
        with col1:
            local_api_url = st.text_input(
                "API地址", 
                value=config["local_model"]["api_url"],
                placeholder="http://26.99.66.138:1234"
            )
            local_model_name = st.text_input(
                "模型名称", 
                value=config["local_model"]["model_name"],
                placeholder="local-model"
            )
        
        with col2:
            local_max_tokens = st.number_input(
                "最大令牌数", 
                min_value=100, 
                max_value=8192, 
                value=config["local_model"]["max_tokens"]
            )
            local_temperature = st.slider(
                "温度参数", 
                min_value=0.0, 
                max_value=2.0, 
                value=config["local_model"]["temperature"],
                step=0.1
            )
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.form_submit_button("💾 保存本地配置", type="primary"):
                config["local_model"] = {
                    "enabled": local_enabled,
                    "api_url": local_api_url,
                    "model_name": local_model_name,
                    "max_tokens": local_max_tokens,
                    "temperature": local_temperature
                }
                save_model_config(config)
                st.success("✅ 本地模型配置已保存")
                st.rerun()
        
        with col2:
            if st.form_submit_button("🔍 测试连接"):
                if local_api_url:
                    success, message = test_model_connection(local_api_url, model_name=local_model_name)
                    if success:
                        st.success("✅ 连接成功")
                    else:
                        st.error(f"❌ 连接失败: {message}")
                else:
                    st.error("⚠️ 请输入API地址")
    
    st.markdown('</div>', unsafe_allow_html=True)
    
    # 远程模型配置
    st.markdown('<div class="config-card">', unsafe_allow_html=True)
    st.markdown("### 🌐 远程模型配置")
    
    with st.form("remote_model_form"):
        remote_enabled = st.checkbox("启用远程模型", value=config["remote_model"]["enabled"])
        
        col1, col2 = st.columns(2)
        
        with col1:
            remote_api_url = st.text_input(
                "API地址", 
                value=config["remote_model"]["api_url"],
                placeholder="https://api.openai.com"
            )
            remote_api_key = st.text_input(
                "API密钥", 
                value=config["remote_model"]["api_key"],
                type="password",
                placeholder="sk-..."
            )
        
        with col2:
            remote_model_name = st.text_input(
                "模型名称", 
                value=config["remote_model"]["model_name"],
                placeholder="gpt-3.5-turbo"
            )
            remote_max_tokens = st.number_input(
                "最大令牌数", 
                min_value=100, 
                max_value=8192, 
                value=config["remote_model"]["max_tokens"],
                key="remote_max_tokens"
            )
        
        remote_temperature = st.slider(
            "温度参数", 
            min_value=0.0, 
            max_value=2.0, 
            value=config["remote_model"]["temperature"],
            step=0.1,
            key="remote_temperature"
        )
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.form_submit_button("💾 保存远程配置", type="primary"):
                config["remote_model"] = {
                    "enabled": remote_enabled,
                    "api_url": remote_api_url,
                    "api_key": remote_api_key,
                    "model_name": remote_model_name,
                    "max_tokens": remote_max_tokens,
                    "temperature": remote_temperature
                }
                save_model_config(config)
                st.success("✅ 远程模型配置已保存")
                st.rerun()
        
        with col2:
            if st.form_submit_button("🔍 测试连接"):
                if not remote_api_url:
                    st.error("⚠️ 请输入API地址")
                elif not remote_api_key:
                    st.error("⚠️ 请输入API密钥")
                elif not remote_model_name:
                    st.error("⚠️ 请输入模型名称")
                else:
                    with st.spinner("正在测试连接..."):
                        success, message = test_model_connection(remote_api_url, remote_api_key, remote_model_name)
                        if success:
                            st.success("✅ 连接成功")
                        else:
                            st.error(f"❌ 连接失败: {message}")
                            if "字符编码错误" in message:
                                st.info("💡 提示：请确保API密钥和模型名称不包含特殊字符")
                            elif "Invalid token" in message:
                                st.info("💡 提示：请检查API密钥是否正确且有效")
    
    st.markdown('</div>', unsafe_allow_html=True)
    
    # 配置状态
    st.markdown('<div class="config-card">', unsafe_allow_html=True)
    st.markdown("### 📊 配置状态")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if config["local_model"]["enabled"]:
            st.markdown('<div class="status-success">🏠 本地模型: 已启用</div>', unsafe_allow_html=True)
        else:
            st.markdown('<div class="status-warning">🏠 本地模型: 未启用</div>', unsafe_allow_html=True)
    
    with col2:
        if config["remote_model"]["enabled"]:
            st.markdown('<div class="status-success">🌐 远程模型: 已启用</div>', unsafe_allow_html=True)
        else:
            st.markdown('<div class="status-warning">🌐 远程模型: 未启用</div>', unsafe_allow_html=True)
    
    # 配置状态提示
    if config["local_model"]["enabled"] or config["remote_model"]["enabled"]:
        st.success("✅ 模型配置完成！您可以切换到其他标签页继续操作。")
        st.info("💡 提示：切换到「想定设计」标签页创建想定，或直接到「推演模块」开始推演。")
    else:
        st.error("⚠️ 请至少启用一个模型配置才能使用AI规划功能")
    
    st.markdown('</div>', unsafe_allow_html=True)
