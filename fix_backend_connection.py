#!/usr/bin/env python3
"""
修复后端连接问题脚本
解决"❌ 无法连接到后端服务"错误
"""

import subprocess
import sys
import time
import requests
import os
from pathlib import Path

def check_port_in_use(port):
    """检查端口是否被占用"""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result == 0
    except:
        return False

def kill_process_on_port(port):
    """终止占用端口的进程"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(
                f'netstat -ano | findstr :{port}',
                shell=True, capture_output=True, text=True
            )
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f':{port}' in line and 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            subprocess.run(f'taskkill /F /PID {pid}', shell=True)
                            print(f"✅ 已终止端口 {port} 上的进程 (PID: {pid})")
        else:  # Linux/Mac
            subprocess.run(f'lsof -ti:{port} | xargs kill -9', shell=True)
            print(f"✅ 已终止端口 {port} 上的进程")
    except Exception as e:
        print(f"⚠️ 终止进程失败: {e}")

def create_simple_backend():
    """创建简单的后端服务"""
    print("🔧 创建简单后端服务...")
    
    backend_code = '''
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import uvicorn

app = FastAPI(
    title="LLM任务分配系统API",
    description="无人机干扰决策系统后端API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "LLM任务分配系统API",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "backend",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/health")
async def api_health():
    return {
        "status": "healthy",
        "version": "1.0.0",
        "api": "v1",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/status")
async def system_status():
    return {
        "backend": "online",
        "database": "simulated",
        "llm": "not_configured",
        "frontend": "ready",
        "timestamp": datetime.now().isoformat()
    }

# 模拟用户认证
@app.post("/api/v1/auth/login")
async def login(credentials: dict = None):
    return {
        "access_token": "demo_token_" + datetime.now().strftime("%Y%m%d%H%M%S"),
        "token_type": "bearer",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "role": "admin"
        }
    }

@app.post("/api/v1/auth/register")
async def register(user_data: dict = None):
    return {
        "message": "用户注册成功",
        "user": {
            "id": 2,
            "username": user_data.get("username", "user"),
            "email": user_data.get("email", "<EMAIL>"),
            "role": "user"
        }
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    # 确保backend目录存在
    backend_dir = Path("backend")
    backend_dir.mkdir(exist_ok=True)
    
    # 写入简单后端文件
    backend_file = backend_dir / "simple_app.py"
    with open(backend_file, 'w', encoding='utf-8') as f:
        f.write(backend_code)
    
    print(f"✅ 简单后端已创建: {backend_file}")
    return backend_file

def test_backend_connection():
    """测试后端连接"""
    print("🔍 测试后端连接...")
    
    endpoints = [
        "http://localhost:8000/",
        "http://localhost:8000/health",
        "http://localhost:8000/api/v1/health"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} - 连接成功")
                print(f"   响应: {response.json()}")
            else:
                print(f"⚠️ {endpoint} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - 连接失败: {e}")

def install_dependencies():
    """安装必要的依赖"""
    print("📦 安装必要依赖...")
    
    packages = ["fastapi", "uvicorn", "requests"]
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 安装 {package}...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True, text=True
            )
            if result.returncode == 0:
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败: {result.stderr}")

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    # 检查端口
    if check_port_in_use(8000):
        print("⚠️ 端口 8000 已被占用")
        response = input("是否终止占用进程？(y/n): ").lower().strip()
        if response == 'y':
            kill_process_on_port(8000)
            time.sleep(2)
    
    # 启动后端
    try:
        cmd = [
            sys.executable, "-m", "uvicorn",
            "backend.simple_app:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        print("📍 后端API: http://localhost:8000")
        print("📍 健康检查: http://localhost:8000/health")
        print("📍 API文档: http://localhost:8000/docs")
        print("🛑 按 Ctrl+C 停止服务")
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 后端服务已停止")
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")

def main():
    """主函数"""
    print("🔧 修复后端连接问题")
    print("=" * 40)
    
    # 1. 安装依赖
    install_dependencies()
    print()
    
    # 2. 创建简单后端
    create_simple_backend()
    print()
    
    # 3. 询问是否启动
    response = input("❓ 是否现在启动后端服务？(y/n): ").lower().strip()
    if response == 'y':
        start_backend()
    else:
        print("\n💡 手动启动命令:")
        print("   python -m uvicorn backend.simple_app:app --host 0.0.0.0 --port 8000 --reload")
        print("\n或者运行:")
        print("   python fix_backend_connection.py")
        
        # 测试连接
        print("\n🔍 如果后端已在运行，测试连接:")
        test_response = input("❓ 是否测试后端连接？(y/n): ").lower().strip()
        if test_response == 'y':
            test_backend_connection()

if __name__ == "__main__":
    main()
