import streamlit as st
from pathlib import Path


def show():
    """显示帮助页面"""
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
    ">
        <h2 style="margin: 0;">📚 帮助中心</h2>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">系统使用指南和常见问题解答</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 帮助内容选项卡
    tab1, tab2, tab3, tab4 = st.tabs(["🚀 快速开始", "📖 用户手册", "❓ 常见问题", "📞 技术支持"])
    
    with tab1:
        show_quick_start()
    
    with tab2:
        show_user_manual()
    
    with tab3:
        show_faq()
    
    with tab4:
        show_support()


def show_quick_start():
    """显示快速开始指南"""
    st.markdown("### 🚀 快速开始指南")
    
    st.markdown("""
    #### 欢迎使用无人机干扰决策系统！
    
    这是一个基于大语言模型的智能轨迹规划与干扰决策平台。以下是快速上手步骤：
    
    ##### 1️⃣ 登录系统
    - 使用演示账号：`admin` / `admin123`
    - 或者注册新账号
    
    ##### 2️⃣ 配置模型
    - 进入 **🤖 模型配置** 页面
    - 配置本地或远程LLM模型
    - 测试模型连接
    
    ##### 3️⃣ 设计想定
    - 进入 **🗺️ 想定设计** 页面
    - 创建新的作战想定
    - 设置地图大小、无人机数量、浮标配置等参数
    
    ##### 4️⃣ 开始推演
    - 进入 **🎮 推演模块** 页面
    - 选择已创建的想定
    - 启动智能推演，查看轨迹规划结果
    
    ##### 5️⃣ 监控系统
    - 查看 **📊 系统状态** 了解系统运行情况
    - 管理员可以查看 **📋 系统日志** 和 **💾 数据管理**
    """)
    
    # 系统要求
    st.markdown("#### 💻 系统要求")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **最低配置：**
        - CPU: 8核心16线程 
        - 内存: 16GB DDR4
        - 存储: 30GB 可用空间 (本地部署LLM所需)
        - 显卡: RTX 2080ti
        - 网络: 宽带连接
        """)
    
    with col2:
        st.markdown("""
        **推荐配置：**
        - CPU: 10核心20线程 
        - 内存: 32GB DDR4
        - 存储: 100GB 可用空间 (本地部署LLM所需)
        - 显卡: RTX 4090
        - 网络: 宽带连接
        """)
    
    # 注意事项
    st.markdown("#### ⚠️ 重要提示")
    st.warning("""
    - 首次使用请先配置LLM模型
    - 建议定期备份重要数据
    - 如遇问题请查看常见问题或联系技术支持
    """)


def show_user_manual():
    """显示用户手册"""
    st.markdown("### 📖 用户手册")
    
    # 功能模块介绍
    with st.expander("🤖 模型配置模块", expanded=False):
        st.markdown("""
        **功能说明：**
        配置和管理大语言模型，支持本地和远程模型。
        
        **主要功能：**
        - 本地模型配置（支持LM Studio等）
        - 远程模型配置（支持OpenAI API等）
        - 模型连接测试
        - 参数调优（温度、最大令牌数等）
        
        **使用步骤：**
        1. 选择模型类型（本地/远程）
        2. 填写API地址和密钥
        3. 设置模型参数
        4. 测试连接
        5. 保存配置
        """)
    
    with st.expander("🗺️ 想定设计模块", expanded=False):
        st.markdown("""
        **功能说明：**
        创建和管理作战想定，设置场景参数。
        
        **主要功能：**
        - 想定创建和编辑
        - 地图大小设置
        - 无人机配置
        - 浮标布置
        - 想定管理（查看、删除）
        
        **参数说明：**
        - **地图大小**：作战区域范围
        - **无人机数量**：参与作战的无人机数量
        - **浮标数量**：海上通信浮标数量
        - **通信范围**：浮标通信覆盖范围
        - **干扰强度**：电子干扰效果强度
        """)
    
    with st.expander("🎮 推演模块", expanded=False):
        st.markdown("""
        **功能说明：**
        基于LLM进行智能轨迹规划和干扰决策。
        
        **主要功能：**
        - 想定选择
        - 态势生成
        - 智能推演
        - 结果可视化
        - 轨迹分析
        
        **推演流程：**
        1. 选择想定
        2. 生成初始态势
        3. 启动智能推演
        4. 查看轨迹规划
        5. 分析推演结果
        """)
    
    with st.expander("📊 系统状态模块", expanded=False):
        st.markdown("""
        **功能说明：**
        监控系统运行状态和资源使用情况。
        
        **监控内容：**
        - CPU使用率
        - 内存使用率
        - 磁盘使用率
        - 服务状态
        - 数据库状态
        
        **状态说明：**
        - **正常**：绿色，系统运行良好
        - **偏高**：黄色，需要关注
        - **异常**：红色，需要处理
        """)
    
    # 管理员功能
    st.markdown("#### 👑 管理员专用功能")
    
    with st.expander("📋 系统日志", expanded=False):
        st.markdown("""
        **功能说明：**
        查看和管理系统运行日志。
        
        **日志类型：**
        - **操作日志**：用户操作记录
        - **系统日志**：系统运行记录
        
        **功能特性：**
        - 日志筛选和搜索
        - 日志导出
        - 日志清理
        """)
    
    with st.expander("💾 数据管理", expanded=False):
        st.markdown("""
        **功能说明：**
        系统数据的备份、恢复和导出。
        
        **主要功能：**
        - 系统备份
        - 数据恢复
        - 数据导出
        - 数据清理
        
        **备份内容：**
        - 用户数据
        - 配置文件
        - 场景数据
        - 系统日志
        """)


def show_faq():
    """显示常见问题"""
    st.markdown("### ❓ 常见问题解答")
    
    with st.expander("Q: 如何配置本地LLM模型？", expanded=False):
        st.markdown("""
        **A:** 
        1. 确保本地LLM服务（如LM Studio）正在运行
        2. 在模型配置页面选择"本地模型"
        3. 填写正确的API地址（通常是 http://localhost:1234）
        4. 点击"测试连接"验证配置
        5. 保存配置
        """)
    
    with st.expander("Q: 推演结果不理想怎么办？", expanded=False):
        st.markdown("""
        **A:** 
        1. 检查模型配置是否正确
        2. 调整模型参数（温度、最大令牌数）
        3. 优化想定参数设置
        4. 尝试不同的LLM模型
        5. 查看系统日志了解详细错误信息
        """)
    
    with st.expander("Q: 如何备份系统数据？", expanded=False):
        st.markdown("""
        **A:** 
        1. 使用管理员账号登录
        2. 进入"数据管理"页面
        3. 点击"创建系统备份"
        4. 下载生成的备份文件
        5. 妥善保存备份文件
        """)
    
    with st.expander("Q: 忘记密码怎么办？", expanded=False):
        st.markdown("""
        **A:** 
        1. 在登录页面点击"忘记密码？"
        2. 输入用户名和注册邮箱
        3. 设置新密码
        4. 使用新密码登录
        """)
    
    with st.expander("Q: 系统运行缓慢怎么办？", expanded=False):
        st.markdown("""
        **A:** 
        1. 检查系统资源使用情况
        2. 清理临时文件和旧日志
        3. 重启系统服务
        4. 检查网络连接
        5. 升级硬件配置
        """)
    
    with st.expander("Q: 如何导出数据？", expanded=False):
        st.markdown("""
        **A:** 
        1. 使用管理员账号登录
        2. 进入"数据管理"页面
        3. 选择要导出的数据类型
        4. 点击相应的导出按钮
        5. 下载生成的文件
        """)


def show_support():
    """显示技术支持"""
    st.markdown("### 📞 技术支持")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        #### 📧 联系方式
        
        **技术支持邮箱：**
        <EMAIL>
        
        **工作时间：**
        周一至周五 9:00-18:00
        
        **响应时间：**
        - 紧急问题：2小时内
        - 一般问题：24小时内
        """)
    
    with col2:
        st.markdown("""
        #### 🔧 自助服务
        
        **系统状态检查：**
        - 查看"系统状态"页面
        - 检查服务运行情况
        
        **日志分析：**
        - 查看"系统日志"页面
        - 分析错误信息
        
        **数据备份：**
        - 定期创建系统备份
        - 保存重要配置
        """)
    
    # 问题反馈
    st.markdown("#### 📝 问题反馈")
    
    with st.form("feedback_form"):
        problem_type = st.selectbox(
            "问题类型",
            ["功能问题", "性能问题", "界面问题", "数据问题", "其他"]
        )
        
        description = st.text_area(
            "问题描述",
            placeholder="请详细描述您遇到的问题...",
            height=100
        )
        
        contact_info = st.text_input(
            "联系方式",
            placeholder="请留下您的邮箱或电话"
        )
        
        if st.form_submit_button("📤 提交反馈", type="primary"):
            if description and contact_info:
                # 这里可以添加实际的反馈提交逻辑
                st.success("✅ 反馈已提交，我们会尽快处理！")
            else:
                st.error("⚠️ 请填写问题描述和联系方式")
    
    # 版本信息
    st.markdown("#### ℹ️ 版本信息")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("系统版本", "v1.0.0")
    
    with col2:
        st.metric("更新时间", "2025年07月24日")
    
    with col3:
        st.metric("构建版本", "2025年07月24日")
