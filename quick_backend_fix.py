#!/usr/bin/env python3
"""
快速后端修复脚本
立即解决SQLModel版本冲突问题
"""

import subprocess
import sys
import os
from pathlib import Path

def quick_fix():
    """快速修复"""
    print("⚡ 快速修复后端问题...")
    
    # 1. 创建简化后端
    print("🔧 创建简化后端...")
    
    simple_backend_content = '''
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import json
from datetime import datetime

app = FastAPI(
    title="LLM任务分配系统API",
    description="无人机干扰决策系统后端API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "LLM任务分配系统API",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "backend",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/health")
async def api_health():
    return {
        "status": "healthy",
        "version": "1.0.0",
        "api": "v1",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/status")
async def api_status():
    return {
        "backend": "online",
        "database": "simulated",
        "llm": "not_configured",
        "timestamp": datetime.now().isoformat()
    }

# 模拟用户认证
@app.post("/api/v1/auth/login")
async def login(credentials: dict):
    return {
        "access_token": "demo_token",
        "token_type": "bearer",
        "user": {
            "id": 1,
            "username": "admin",
            "role": "admin"
        }
    }

# 模拟用户信息
@app.get("/api/v1/users/me")
async def get_current_user():
    return {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "role": "admin",
        "created_at": "2024-01-01T00:00:00"
    }

# 模拟场景列表
@app.get("/api/v1/scenarios")
async def get_scenarios():
    return {
        "scenarios": [
            {
                "id": 1,
                "name": "演示场景",
                "description": "系统演示场景",
                "created_at": "2024-01-01T00:00:00"
            }
        ],
        "total": 1
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    # 保存到backend目录
    backend_dir = Path("backend")
    backend_dir.mkdir(exist_ok=True)
    
    simple_backend_path = backend_dir / "simple_main.py"
    with open(simple_backend_path, 'w', encoding='utf-8') as f:
        f.write(simple_backend_content)
    
    print(f"✅ 简化后端已创建: {simple_backend_path}")
    
    # 2. 启动简化后端
    print("🚀 启动简化后端...")
    
    try:
        cmd = [
            sys.executable, "-m", "uvicorn",
            "backend.simple_main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        print("\n🎉 后端服务启动成功!")
        print("📍 后端API: http://localhost:8000")
        print("📍 健康检查: http://localhost:8000/health")
        print("📍 API状态: http://localhost:8000/api/v1/status")
        print("🛑 按 Ctrl+C 停止服务")
        print("\n💡 现在可以刷新前端页面，后端服务状态应该显示为'正常'")
        
        subprocess.run(cmd, cwd=Path.cwd())
        
    except KeyboardInterrupt:
        print("\n🛑 后端服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请检查是否已安装 fastapi 和 uvicorn:")
        print("   pip install fastapi uvicorn")

def main():
    """主函数"""
    print("⚡ 快速后端修复脚本")
    print("=" * 30)
    
    # 检查基础依赖
    try:
        import fastapi
        import uvicorn
        print("✅ FastAPI 和 Uvicorn 可用")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请先安装:")
        print("   pip install fastapi uvicorn")
        return
    
    quick_fix()

if __name__ == "__main__":
    main()
