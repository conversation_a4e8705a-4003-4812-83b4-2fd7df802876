import sqlite3
import json
from datetime import datetime
from pathlib import Path
import streamlit as st


def init_log_database():
    """初始化日志数据库"""
    db_path = Path("data/system_logs.db")
    db_path.parent.mkdir(exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建操作日志表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS operation_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            username TEXT,
            operation TEXT NOT NULL,
            module TEXT,
            details TEXT,
            ip_address TEXT,
            user_agent TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'success'
        )
    ''')
    
    # 创建系统日志表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            level TEXT NOT NULL,
            message TEXT NOT NULL,
            module TEXT,
            details TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()


def log_operation(operation, module=None, details=None, status='success'):
    """记录用户操作日志"""
    init_log_database()
    
    user = st.session_state.get('user', {})
    
    db_path = Path("data/system_logs.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO operation_logs 
            (user_id, username, operation, module, details, status, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            user.get('id'),
            user.get('username', 'anonymous'),
            operation,
            module,
            json.dumps(details) if details else None,
            status,
            datetime.now().isoformat()
        ))
        
        conn.commit()
    except Exception as e:
        print(f"日志记录失败: {e}")
    finally:
        conn.close()


def log_system(level, message, module=None, details=None):
    """记录系统日志"""
    init_log_database()
    
    db_path = Path("data/system_logs.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO system_logs 
            (level, message, module, details, timestamp)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            level,
            message,
            module,
            json.dumps(details) if details else None,
            datetime.now().isoformat()
        ))
        
        conn.commit()
    except Exception as e:
        print(f"系统日志记录失败: {e}")
    finally:
        conn.close()


def get_operation_logs(limit=100, user_id=None):
    """获取操作日志"""
    db_path = Path("data/system_logs.db")
    if not db_path.exists():
        return []
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    if user_id:
        cursor.execute('''
            SELECT * FROM operation_logs 
            WHERE user_id = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
        ''', (user_id, limit))
    else:
        cursor.execute('''
            SELECT * FROM operation_logs 
            ORDER BY timestamp DESC 
            LIMIT ?
        ''', (limit,))
    
    logs = cursor.fetchall()
    conn.close()
    
    return [
        {
            'id': log[0],
            'user_id': log[1],
            'username': log[2],
            'operation': log[3],
            'module': log[4],
            'details': json.loads(log[5]) if log[5] else None,
            'ip_address': log[6],
            'user_agent': log[7],
            'timestamp': log[8],
            'status': log[9]
        }
        for log in logs
    ]


def get_system_logs(limit=100, level=None):
    """获取系统日志"""
    db_path = Path("data/system_logs.db")
    if not db_path.exists():
        return []
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    if level:
        cursor.execute('''
            SELECT * FROM system_logs 
            WHERE level = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
        ''', (level, limit))
    else:
        cursor.execute('''
            SELECT * FROM system_logs 
            ORDER BY timestamp DESC 
            LIMIT ?
        ''', (limit,))
    
    logs = cursor.fetchall()
    conn.close()
    
    return [
        {
            'id': log[0],
            'level': log[1],
            'message': log[2],
            'module': log[3],
            'details': json.loads(log[4]) if log[4] else None,
            'timestamp': log[5]
        }
        for log in logs
    ]


def clear_old_logs(days=30):
    """清理旧日志"""
    init_log_database()
    
    db_path = Path("data/system_logs.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cutoff_date = datetime.now().replace(day=datetime.now().day - days)
        
        cursor.execute('''
            DELETE FROM operation_logs 
            WHERE timestamp < ?
        ''', (cutoff_date.isoformat(),))
        
        cursor.execute('''
            DELETE FROM system_logs 
            WHERE timestamp < ?
        ''', (cutoff_date.isoformat(),))
        
        conn.commit()
        return True
    except Exception as e:
        print(f"清理日志失败: {e}")
        return False
    finally:
        conn.close()
