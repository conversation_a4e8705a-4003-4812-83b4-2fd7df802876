#!/usr/bin/env python3
"""
LLM任务分配系统 - 简化启动脚本
解决后端连接问题
"""

import subprocess
import sys
import time
import threading
import signal
import os
from pathlib import Path

# 全局进程列表
processes = []

def cleanup():
    """清理进程"""
    print("\n🛑 正在关闭系统...")
    for process in processes:
        if process and process.poll() is None:
            try:
                process.terminate()
                time.sleep(1)
                if process.poll() is None:
                    process.kill()
            except:
                pass
    print("✅ 系统已关闭")

def signal_handler(sig, frame):
    """信号处理"""
    cleanup()
    sys.exit(0)

def install_packages():
    """安装必要的包"""
    packages = ["streamlit", "fastapi", "uvicorn", "pandas", "requests", "plotly"]
    
    for package in packages:
        try:
            __import__(package)
        except ImportError:
            print(f"📦 安装 {package}...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             capture_output=True, check=True)
                print(f"✅ {package} 安装成功")
            except:
                print(f"⚠️ {package} 安装失败，但系统可能仍能运行")

def check_backend_file():
    """检查后端文件是否存在"""
    backend_file = Path("backend/simple_app.py")
    if not backend_file.exists():
        print("🔧 创建后端文件...")
        backend_file.parent.mkdir(exist_ok=True)
        
        backend_code = '''from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime

app = FastAPI(title="LLM系统API")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "LLM任务分配系统", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/api/v1/health")
async def api_health():
    return {"status": "healthy", "version": "1.0.0"}
'''
        
        with open(backend_file, "w", encoding="utf-8") as f:
            f.write(backend_code)
        print(f"✅ 后端文件已创建: {backend_file}")

def start_backend():
    """启动后端"""
    print("🚀 启动后端服务...")
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "backend.simple_app:app", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        processes.append(process)
        return process
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def start_frontend():
    """启动前端"""
    print("🚀 启动前端服务...")
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run",
            "frontend/main.py",
            "--server.port", "8501",
            "--server.headless", "true"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        processes.append(process)
        return process
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        return None

def wait_for_backend():
    """等待后端启动"""
    import requests
    
    for i in range(30):  # 等待30秒
        try:
            response = requests.get("http://localhost:8000/health", timeout=2)
            if response.status_code == 200:
                print("✅ 后端服务已就绪")
                return True
        except:
            pass
        time.sleep(1)
        print(f"⏳ 等待后端启动... ({i+1}/30)")
    
    print("❌ 后端启动超时")
    return False

def main():
    """主函数"""
    print("🚁 LLM任务分配系统")
    print("=" * 30)
    
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    
    # 创建必要目录
    for dir_name in ["data", "backend", "logs"]:
        Path(dir_name).mkdir(exist_ok=True)
    
    # 安装依赖
    print("📦 检查依赖...")
    install_packages()
    
    # 检查后端文件
    check_backend_file()
    
    # 启动后端
    backend = start_backend()
    if not backend:
        print("❌ 后端启动失败，无法继续")
        return
    
    # 等待后端就绪
    if not wait_for_backend():
        print("❌ 后端未能正常启动")
        cleanup()
        return
    
    # 启动前端
    frontend = start_frontend()
    if not frontend:
        print("❌ 前端启动失败")
        cleanup()
        return
    
    # 等待前端启动
    time.sleep(3)
    
    # 显示信息
    print("\n🎉 启动完成!")
    print("📍 前端: http://localhost:8501")
    print("📍 后端: http://localhost:8000")
    print("📍 API文档: http://localhost:8000/docs")
    print("🔐 默认账号: admin / admin123")
    print("🛑 按 Ctrl+C 停止系统")
    
    # 保持运行
    try:
        while True:
            # 检查进程状态
            if backend.poll() is not None:
                print("❌ 后端进程已退出")
                break
            if frontend.poll() is not None:
                print("❌ 前端进程已退出")
                break
            time.sleep(1)
    except KeyboardInterrupt:
        pass
    finally:
        cleanup()

if __name__ == "__main__":
    main()
