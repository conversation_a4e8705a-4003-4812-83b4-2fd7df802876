import streamlit as st
import json
import uuid
from pathlib import Path
from datetime import datetime


def save_scenario(scenario_data):
    """保存想定数据"""
    scenarios_path = Path("data/scenarios")
    scenarios_path.mkdir(exist_ok=True)
    
    scenario_file = scenarios_path / f"{scenario_data['id']}.json"
    with open(scenario_file, 'w', encoding='utf-8') as f:
        json.dump(scenario_data, f, ensure_ascii=False, indent=2)


def load_scenarios():
    """加载所有想定"""
    scenarios_path = Path("data/scenarios")
    if not scenarios_path.exists():
        return []
    
    scenarios = []
    for scenario_file in scenarios_path.glob("*.json"):
        try:
            with open(scenario_file, 'r', encoding='utf-8') as f:
                scenarios.append(json.load(f))
        except:
            continue
    
    return sorted(scenarios, key=lambda x: x.get('created_at', ''), reverse=True)


def delete_scenario(scenario_id):
    """删除想定"""
    scenario_file = Path(f"data/scenarios/{scenario_id}.json")
    if scenario_file.exists():
        scenario_file.unlink()


def show():
    """显示想定设计页面"""
    
    # 页面样式
    st.markdown("""
    <style>
        .scenario-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
            border-radius: 15px;
            color: white;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }
        
        .scenario-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .scenario-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        
        .config-section {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
    </style>
    """, unsafe_allow_html=True)

    # 简化的说明
    st.info("💡 创建和管理想定，设置无人机、浮标、地图等参数")

    # 检查是否显示创建想定表单
    if st.session_state.get('show_create_scenario', False):
        show_create_scenario_form()
        return

    # 检查是否显示编辑想定表单
    if st.session_state.get('show_edit_scenario', False):
        show_edit_scenario_form()
        return

    # 主要内容
    col1, col2 = st.columns([1, 2])
    
    with col1:
        show_scenario_list()
    
    with col2:
        show_scenario_details()


def show_scenario_list():
    """显示想定列表"""
    st.markdown("### 📋 想定列表")
    
    # 新建想定按钮
    if st.button("➕ 新建想定", type="primary", use_container_width=True):
        st.session_state.show_create_scenario = True
        st.rerun()
    
    # 加载想定列表
    scenarios = load_scenarios()
    
    if not scenarios:
        st.info("暂无想定，请创建新的想定")
        return
    
    # 显示想定卡片
    for scenario in scenarios:
        is_selected = st.session_state.get('selected_scenario', {}).get('id') == scenario['id']

        # 使用列布局：选择按钮在左侧，想定信息在右侧
        col1, col2 = st.columns([1, 5])

        with col1:
            # 选择按钮
            button_type = "primary" if is_selected else "secondary"
            button_text = "✓" if is_selected else "○"
            if st.button(button_text, key=f"select_{scenario['id']}",
                        type=button_type, use_container_width=True,
                        help=f"选择想定: {scenario['name']}"):
                st.session_state.selected_scenario = scenario
                st.rerun()

        with col2:
            # 想定卡片样式
            card_style = (
                "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: 2px solid #667eea;"
                if is_selected else
                "background: white; color: #333; border: 1px solid #e2e8f0;"
            )

            st.markdown(f"""
            <div style="
                {card_style}
                padding: 1rem;
                border-radius: 10px;
                margin-bottom: 0.5rem;
            ">
                <div style="font-weight: 600; margin-bottom: 0.5rem;">
                    🗺️ {scenario['name']}
                </div>
                <div style="font-size: 0.9rem; opacity: 0.8; margin-bottom: 0.5rem;">
                    📅 {scenario.get('created_at', '未知')}
                </div>
                <div style="display: flex; gap: 0.5rem; font-size: 0.8rem;">
                    <span style="background: rgba(255,255,255,0.2); padding: 0.2rem 0.5rem; border-radius: 12px;">
                        🚁 {scenario['drones']}架无人机
                    </span>
                    <span style="background: rgba(255,255,255,0.2); padding: 0.2rem 0.5rem; border-radius: 12px;">
                        📍 {scenario['buoys']}个浮标
                    </span>
                </div>
            </div>
            """, unsafe_allow_html=True)


def show_scenario_details():
    """显示想定详情"""
    selected_scenario = st.session_state.get('selected_scenario')
    
    if not selected_scenario:
        st.markdown("### 📄 想定详情")
        st.info("👈 请从左侧选择一个想定查看详情")
        return
    
    st.markdown(f"### 📄 {selected_scenario['name']}")
    
    # 基本信息
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown(f"""
        **📝 想定名称:** {selected_scenario['name']}
        
        **🚁 无人机数量:** {selected_scenario['drones']}架
        
        **📍 浮标数量:** {selected_scenario['buoys']}个
        
        **🗺️ 地图大小:** {selected_scenario['map_size']}km²
        """)
    
    with col2:
        st.markdown(f"""
        **📡 通信模式:** {selected_scenario.get('communication_modes', '未知')}种
        
        **⚡ 干扰模型:** {selected_scenario.get('interference_models', '未知')}种
        
        **📅 创建时间:** {selected_scenario.get('created_at', '未知')}
        
        **👤 创建者:** {selected_scenario.get('creator', '未知')}
        """)
    
    # 详细配置
    if 'config' in selected_scenario:
        st.markdown("### ⚙️ 详细配置")
        
        with st.expander("🚁 无人机配置", expanded=True):
            config = selected_scenario['config']
            st.write(f"**起始位置:** {config.get('drone_start_area', '未设置')}")
            st.write(f"**飞行高度:** {config.get('flight_altitude', '未设置')}米")
            st.write(f"**最大速度:** {config.get('max_speed', '未设置')}m/s")
            st.write(f"**通信范围:** {config.get('communication_range', '未设置')}km")
        
        with st.expander("📍 浮标配置"):
            st.write(f"**分布区域:** {config.get('buoy_area', '未设置')}")
            st.write(f"**信号强度:** {config.get('signal_strength', '未设置')}")
            st.write(f"**检测范围:** {config.get('detection_range', '未设置')}km")
        
        with st.expander("⚡ 干扰配置"):
            st.write(f"**干扰类型:** {config.get('interference_types', '未设置')}")
            st.write(f"**干扰强度:** {config.get('interference_strength', '未设置')}")
            st.write(f"**影响范围:** {config.get('interference_range', '未设置')}km")
    
    # 操作按钮
    st.markdown("### 🛠️ 操作")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("▶️ 开始推演", key="start_simulation"):
            st.session_state.simulation_scenario = selected_scenario
            st.success(f"✅ 已选择想定「{selected_scenario['name']}」，请切换到「推演模块」标签页开始推演！")
            st.balloons()
    
    with col2:
        if st.button("✏️ 编辑", key="edit_scenario_btn"):
            st.session_state.show_edit_scenario = True
            st.session_state.edit_scenario = selected_scenario
            st.rerun()
    
    with col3:
        if st.button("📋 复制", key="copy_scenario"):
            # 创建副本
            new_scenario = selected_scenario.copy()
            new_scenario['id'] = str(uuid.uuid4())
            new_scenario['name'] = f"{selected_scenario['name']}_副本"
            new_scenario['created_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            save_scenario(new_scenario)
            st.success("想定已复制")
            st.rerun()
    
    with col4:
        if st.button("🗑️ 删除", key="delete_scenario"):
            st.session_state.show_delete_confirm = True
            st.rerun()

    # 删除确认对话框
    if st.session_state.get('show_delete_confirm', False):
        st.error(f"⚠️ 确认删除想定「{selected_scenario['name']}」？")
        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            if st.button("✅ 确认删除", type="primary"):
                delete_scenario(selected_scenario['id'])
                st.session_state.selected_scenario = None
                st.session_state.show_delete_confirm = False
                st.success("想定已删除")
                st.rerun()

        with col2:
            if st.button("❌ 取消"):
                st.session_state.show_delete_confirm = False
                st.rerun()


def show_create_scenario_form():
    """显示创建想定表单"""
    st.markdown("### ➕ 创建新想定")
    
    with st.form("create_scenario_form"):
        # 基本信息
        st.markdown("#### 📋 基本信息")
        
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input("想定名称", placeholder="输入想定名称...")
            drones = st.number_input("无人机数量", min_value=1, max_value=50, value=5)
            buoys = st.number_input("浮标数量", min_value=1, max_value=500, value=3)
        
        with col2:
            map_size = st.selectbox("地图大小", ["10x10", "20x20", "50x50", "100x100"])
            communication_modes = st.number_input("通信模式数量", min_value=1, max_value=10, value=3)
            interference_models = st.number_input("干扰模型数量", min_value=1, max_value=10, value=2)
        
        # 详细配置
        st.markdown("#### ⚙️ 详细配置")
        
        with st.expander("🚁 无人机配置", expanded=True):
            col1, col2 = st.columns(2)
            
            with col1:
                drone_start_area = st.selectbox("起始区域", ["西北角", "东北角", "西南角", "东南角", "中心区域"])
                flight_altitude = st.number_input("飞行高度(米)", min_value=50, max_value=1000, value=200)
            
            with col2:
                max_speed = st.number_input("最大速度(m/s)", min_value=10, max_value=100, value=30)
                communication_range = st.number_input("通信范围(km)", min_value=1, max_value=50, value=10)
        
        with st.expander("📍 浮标配置"):
            col1, col2 = st.columns(2)
            
            with col1:
                buoy_area = st.selectbox("分布区域", ["随机分布", "均匀分布", "集中分布", "边界分布"])
                signal_strength = st.selectbox("信号强度", ["弱", "中", "强"])
            
            with col2:
                detection_range = st.number_input("检测范围(km)", min_value=1, max_value=20, value=5)
        
        with st.expander("⚡ 干扰配置"):
            col1, col2 = st.columns(2)
            
            with col1:
                interference_types = st.multiselect(
                    "干扰类型", 
                    ["信号干扰", "GPS干扰", "通信干扰", "雷达干扰"],
                    default=["信号干扰", "通信干扰"]
                )
                interference_strength = st.selectbox("干扰强度", ["低", "中", "高"])
            
            with col2:
                interference_range = st.number_input("影响范围(km)", min_value=1, max_value=15, value=1)
        
        # 提交按钮
        col1, col2 = st.columns(2)
        
        with col1:
            if st.form_submit_button("✅ 创建想定", type="primary"):
                if name:
                    scenario_data = {
                        'id': str(uuid.uuid4()),
                        'name': name,
                        'drones': drones,
                        'buoys': buoys,
                        'map_size': map_size,
                        'communication_modes': communication_modes,
                        'interference_models': interference_models,
                        'created_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        'creator': st.session_state.user.get('username', 'unknown'),
                        'config': {
                            'drone_start_area': drone_start_area,
                            'flight_altitude': flight_altitude,
                            'max_speed': max_speed,
                            'communication_range': communication_range,
                            'buoy_area': buoy_area,
                            'signal_strength': signal_strength,
                            'detection_range': detection_range,
                            'interference_types': interference_types,
                            'interference_strength': interference_strength,
                            'interference_range': interference_range
                        }
                    }
                    
                    save_scenario(scenario_data)
                    st.success(f"✅ 想定 '{name}' 创建成功！")
                    st.session_state.show_create_scenario = False
                    st.session_state.selected_scenario = scenario_data
                    st.rerun()
                else:
                    st.error("请输入想定名称")
        
        with col2:
            if st.form_submit_button("❌ 取消"):
                st.session_state.show_create_scenario = False
                st.rerun()


def show_edit_scenario_form():
    """显示编辑想定表单（补全所有详细参数）"""
    scenario = st.session_state.get('edit_scenario')
    if not scenario:
        st.session_state.show_edit_scenario = False
        st.rerun()
        return

    st.markdown(f"### ✏️ 编辑想定 - {scenario['name']}")

    # 兼容旧数据
    config = scenario.get('config', {})

    with st.form("edit_scenario_form"):
        # 基本信息
        st.markdown("#### 📋 基本信息")
        col1, col2 = st.columns(2)
        with col1:
            name = st.text_input("想定名称", value=scenario.get('name', ''))
            drones = st.number_input("无人机数量", min_value=1, max_value=50, value=scenario.get('drones', 5))
            buoys = st.number_input("浮标数量", min_value=1, max_value=500, value=scenario.get('buoys', 3))
        with col2:
            # 地图大小
            map_size_value = scenario.get('map_size', '20x20')
            if isinstance(map_size_value, str):
                if 'x' in map_size_value:
                    map_size_value = map_size_value
                else:
                    map_size_value = f"{map_size_value}x{map_size_value}"
            map_size = st.selectbox("地图大小", ["10x10", "20x20", "50x50", "100x100"], index=["10x10", "20x20", "50x50", "100x100"].index(str(map_size_value)) if str(map_size_value) in ["10x10", "20x20", "50x50", "100x100"] else 1)
            communication_modes = st.number_input("通信模式数量", min_value=1, max_value=10, value=scenario.get('communication_modes', 3))
            interference_models = st.number_input("干扰模型数量", min_value=1, max_value=10, value=scenario.get('interference_models', 2))

        # 详细配置
        st.markdown("#### ⚙️ 详细配置")
        with st.expander("🚁 无人机配置", expanded=True):
            col1, col2 = st.columns(2)
            with col1:
                drone_start_area = st.selectbox("起始区域", ["西北角", "东北角", "西南角", "东南角", "中心区域"], index=["西北角", "东北角", "西南角", "东南角", "中心区域"].index(config.get('drone_start_area', '西北角')) if config.get('drone_start_area', '西北角') in ["西北角", "东北角", "西南角", "东南角", "中心区域"] else 0)
                flight_altitude = st.number_input("飞行高度(米)", min_value=50, max_value=1000, value=config.get('flight_altitude', 200))
            with col2:
                max_speed = st.number_input("最大速度(m/s)", min_value=10, max_value=100, value=config.get('max_speed', 30))
                communication_range = st.number_input("通信范围(km)", min_value=1, max_value=50, value=config.get('communication_range', 10))
        with st.expander("📍 浮标配置"):
            col1, col2 = st.columns(2)
            with col1:
                buoy_area = st.selectbox("分布区域", ["随机分布", "均匀分布", "集中分布", "边界分布"], index=["随机分布", "均匀分布", "集中分布", "边界分布"].index(config.get('buoy_area', '随机分布')) if config.get('buoy_area', '随机分布') in ["随机分布", "均匀分布", "集中分布", "边界分布"] else 0)
                signal_strength = st.selectbox("信号强度", ["弱", "中", "强"], index=["弱", "中", "强"].index(config.get('signal_strength', '中')) if config.get('signal_strength', '中') in ["弱", "中", "强"] else 1)
            with col2:
                detection_range = st.number_input("检测范围(km)", min_value=1, max_value=20, value=config.get('detection_range', 5))
        with st.expander("⚡ 干扰配置"):
            col1, col2 = st.columns(2)
            with col1:
                interference_types = st.multiselect(
                    "干扰类型",
                    ["信号干扰", "GPS干扰", "通信干扰", "雷达干扰"],
                    default=config.get('interference_types', ["信号干扰", "通信干扰"])
                )
                interference_strength = st.selectbox("干扰强度", ["低", "中", "高"], index=["低", "中", "高"].index(config.get('interference_strength', '中')) if config.get('interference_strength', '中') in ["低", "中", "高"] else 1)
            with col2:
                interference_range = st.number_input("影响范围(km)", min_value=1, max_value=15, value=config.get('interference_range', 1))

        # 提交按钮
        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("💾 保存修改", type="primary"):
                updated_scenario = {
                    'id': scenario['id'],
                    'name': name,
                    'drones': drones,
                    'buoys': buoys,
                    'map_size': map_size,
                    'communication_modes': communication_modes,
                    'interference_models': interference_models,
                    'created_at': scenario.get('created_at', ''),
                    'updated_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'creator': scenario.get('creator', st.session_state.user.get('username', 'unknown')),
                    'config': {
                        'drone_start_area': drone_start_area,
                        'flight_altitude': flight_altitude,
                        'max_speed': max_speed,
                        'communication_range': communication_range,
                        'buoy_area': buoy_area,
                        'signal_strength': signal_strength,
                        'detection_range': detection_range,
                        'interference_types': interference_types,
                        'interference_strength': interference_strength,
                        'interference_range': interference_range
                    }
                }
                save_scenario(updated_scenario)
                st.session_state.show_edit_scenario = False
                st.session_state.selected_scenario = updated_scenario
                st.success("想定已更新")
                st.rerun()
        with col2:
            if st.form_submit_button("❌ 取消"):
                st.session_state.show_edit_scenario = False
                st.rerun()
