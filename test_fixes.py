#!/usr/bin/env python3
"""
测试修复结果的脚本
检查是否还有重复按钮ID和"开发中"文本
"""

import re
from pathlib import Path

def check_duplicate_buttons():
    """检查重复按钮ID问题"""
    print("🔍 检查重复按钮ID问题...")
    
    # 要检查的文件
    files_to_check = [
        "frontend/main.py",
        "frontend/pages/user_profile_page.py",
        "frontend/pages/security_page.py"
    ]
    
    button_texts = {}
    issues_found = []
    
    for file_path in files_to_check:
        file_obj = Path(file_path)
        if not file_obj.exists():
            continue
            
        try:
            with open(file_obj, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找st.button调用
            button_pattern = r'st\.button\s*\(\s*["\']([^"\']+)["\'][^)]*\)'
            matches = re.findall(button_pattern, content)
            
            for button_text in matches:
                if button_text in button_texts:
                    button_texts[button_text].append(str(file_path))
                else:
                    button_texts[button_text] = [str(file_path)]
                    
        except Exception as e:
            print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    # 检查重复
    for button_text, files in button_texts.items():
        if len(files) > 1:
            issues_found.append(f"重复按钮文本 '{button_text}' 在文件: {', '.join(files)}")
    
    if issues_found:
        print("⚠️ 发现重复按钮问题:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 未发现重复按钮问题")
        return True

def check_development_text():
    """检查"开发中"文本"""
    print("🔍 检查'开发中'文本...")
    
    files_to_check = [
        "frontend/pages/security_page.py",
        "frontend/pages/user_profile_page.py"
    ]
    
    issues_found = []
    
    for file_path in files_to_check:
        file_obj = Path(file_path)
        if not file_obj.exists():
            continue
            
        try:
            with open(file_obj, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找"开发中"文本
            if "开发中" in content:
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if "开发中" in line:
                        issues_found.append(f"{file_path}:{i} - {line.strip()}")
                        
        except Exception as e:
            print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    if issues_found:
        print("⚠️ 发现'开发中'文本:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 未发现'开发中'文本")
        return True

def check_button_keys():
    """检查按钮是否有唯一key"""
    print("🔍 检查按钮key参数...")
    
    files_to_check = [
        "frontend/main.py",
        "frontend/pages/user_profile_page.py",
        "frontend/pages/security_page.py"
    ]
    
    issues_found = []
    
    for file_path in files_to_check:
        file_obj = Path(file_path)
        if not file_obj.exists():
            continue
            
        try:
            with open(file_obj, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找没有key参数的st.button调用
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'st.button(' in line and 'key=' not in line:
                    # 检查是否是多行按钮调用
                    full_call = line
                    j = i
                    while j < len(lines) and ')' not in full_call:
                        j += 1
                        if j < len(lines):
                            full_call += lines[j]
                    
                    if 'key=' not in full_call:
                        issues_found.append(f"{file_path}:{i} - 按钮缺少key参数")
                        
        except Exception as e:
            print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    if issues_found:
        print("⚠️ 发现缺少key参数的按钮:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 所有按钮都有key参数")
        return True

def main():
    """主函数"""
    print("🧪 测试修复结果")
    print("=" * 40)
    
    all_good = True
    
    # 检查重复按钮
    if not check_duplicate_buttons():
        all_good = False
    
    print()
    
    # 检查开发中文本
    if not check_development_text():
        all_good = False
    
    print()
    
    # 检查按钮key
    if not check_button_keys():
        all_good = False
    
    print()
    print("=" * 40)
    
    if all_good:
        print("🎉 所有问题已修复!")
        print("✅ 可以重新启动Streamlit应用")
    else:
        print("⚠️ 仍有问题需要修复")
    
    return all_good

if __name__ == "__main__":
    main()
