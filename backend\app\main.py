from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
from contextlib import asynccontextmanager

from backend.core.config import settings
from backend.core.database import create_db_and_tables
from backend.api.v1.api import api_router
from backend.core.security import get_current_user
from backend.models.user import User


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("🚀 启动LLM任务分配系统...")
    create_db_and_tables()
    print("✅ 数据库初始化完成")
    
    yield
    
    # 关闭时执行
    print("🛑 系统正在关闭...")


# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="基于大语言模型的多目标任务分配与干扰决策系统",
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# 静态文件服务
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "LLM任务分配系统API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "llm-task-allocation-backend"
    }


@app.get("/protected")
async def protected_route(current_user: User = Depends(get_current_user)):
    """受保护的路由示例"""
    return {
        "message": f"Hello {current_user.username}!",
        "user_id": current_user.id
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True if settings.ENVIRONMENT == "development" else False,
        log_level="info"
    )
