#!/usr/bin/env python3
"""
后端问题修复脚本
解决SQLModel和SQLAlchemy版本兼容性问题
"""

import subprocess
import sys
import os
from pathlib import Path

def fix_sqlmodel_versions():
    """修复SQLModel版本问题"""
    print("🔧 修复SQLModel版本兼容性问题...")
    
    # 卸载可能冲突的包
    packages_to_remove = [
        "sqlmodel",
        "sqlalchemy",
        "pydantic"
    ]
    
    for package in packages_to_remove:
        try:
            print(f"📦 卸载 {package}...")
            subprocess.run([sys.executable, "-m", "pip", "uninstall", package, "-y"], 
                         capture_output=True)
        except Exception as e:
            print(f"⚠️ 卸载 {package} 失败: {e}")
    
    # 安装兼容版本
    compatible_packages = [
        "pydantic==1.10.12",
        "sqlalchemy==1.4.48", 
        "sqlmodel==0.0.8"
    ]
    
    for package in compatible_packages:
        try:
            print(f"📦 安装 {package}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败: {result.stderr}")
        except Exception as e:
            print(f"❌ 安装 {package} 失败: {e}")

def create_simple_backend():
    """创建简化的后端应用"""
    print("🔧 创建简化后端应用...")
    
    # 创建简化的main.py
    simple_main_content = '''
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(
    title="LLM任务分配系统API",
    description="无人机干扰决策系统后端API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "LLM任务分配系统API", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "backend"}

@app.get("/api/v1/health")
async def api_health():
    return {"status": "healthy", "version": "1.0.0"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    # 保存简化版本
    simple_main_path = Path("backend/app/main_simple.py")
    simple_main_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(simple_main_path, 'w', encoding='utf-8') as f:
        f.write(simple_main_content)
    
    print(f"✅ 简化后端已创建: {simple_main_path}")
    return simple_main_path

def test_imports():
    """测试关键模块导入"""
    print("🧪 测试模块导入...")
    
    test_modules = [
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("pydantic", "Pydantic"),
        ("sqlalchemy", "SQLAlchemy"),
        ("sqlmodel", "SQLModel")
    ]
    
    success_count = 0
    
    for module, name in test_modules:
        try:
            __import__(module)
            print(f"✅ {name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name}: {e}")
    
    print(f"\n📊 导入测试结果: {success_count}/{len(test_modules)} 成功")
    return success_count >= 2  # 至少需要FastAPI和Uvicorn

def start_simple_backend():
    """启动简化后端"""
    print("🚀 启动简化后端...")
    
    try:
        # 使用简化版本启动
        cmd = [
            sys.executable, "-m", "uvicorn",
            "backend.app.main_simple:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        print("📍 后端API: http://localhost:8000")
        print("📍 健康检查: http://localhost:8000/health")
        print("🛑 按 Ctrl+C 停止服务")
        
        subprocess.run(cmd, cwd=Path.cwd())
        
    except KeyboardInterrupt:
        print("\n🛑 后端服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🔧 后端问题修复脚本")
    print("=" * 40)
    
    print("\n🚀 开始修复...")
    
    # 1. 修复版本问题
    fix_sqlmodel_versions()
    
    # 2. 测试导入
    if test_imports():
        print("\n✅ 依赖修复成功")
        
        # 询问是否启动原版后端
        response = input("\n❓ 尝试启动原版后端？(y/n): ").lower().strip()
        if response == 'y':
            try:
                cmd = [
                    sys.executable, "-m", "uvicorn",
                    "backend.app.main:app",
                    "--host", "0.0.0.0",
                    "--port", "8000",
                    "--reload"
                ]
                subprocess.run(cmd, cwd=Path.cwd())
            except Exception as e:
                print(f"❌ 原版后端启动失败: {e}")
                print("🔄 切换到简化版本...")
                create_simple_backend()
                start_simple_backend()
        else:
            # 3. 创建并启动简化版本
            create_simple_backend()
            start_simple_backend()
    else:
        print("\n❌ 依赖修复失败")
        print("💡 建议:")
        print("   1. 检查Python环境")
        print("   2. 重新安装依赖: pip install fastapi uvicorn")
        print("   3. 使用虚拟环境")

if __name__ == "__main__":
    main()
