try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        # 如果pydantic也没有，使用简单的配置类
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

from typing import Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    PROJECT_NAME: str = "LLM Task Allocation System"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # 环境配置
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./data/app.db"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # 安全配置
    SECRET_KEY: str = "your-super-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 大语言模型配置
    DEFAULT_MODEL_NAME: str = "gpt2"
    MODEL_CACHE_DIR: str = "./models"
    MAX_MODEL_SIZE_GB: int = 10
    
    # 任务配置
    MAX_CONCURRENT_TASKS: int = 5
    TASK_TIMEOUT_MINUTES: int = 30
    
    # 可视化配置
    CESIUM_ACCESS_TOKEN: Optional[str] = None
    MAPBOX_ACCESS_TOKEN: Optional[str] = None
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"
    
    # 文件上传配置
    MAX_UPLOAD_SIZE_MB: int = 100
    UPLOAD_DIR: str = "./data/uploads"
    
    # 邮件配置
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    
    # 前端配置
    FRONTEND_URL: str = "http://localhost:8501"
    BACKEND_URL: str = "http://localhost:8000"
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = 30
    WS_MAX_CONNECTIONS: int = 100
    
    class Config:
        env_file = ".env"
        case_sensitive = True
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保必要的目录存在
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            Path(self.MODEL_CACHE_DIR),
            Path(self.UPLOAD_DIR),
            Path(self.LOG_FILE).parent,
            Path("./data"),
            Path("./static")
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @property
    def database_url_sync(self) -> str:
        """同步数据库URL"""
        return self.DATABASE_URL
    
    @property
    def database_url_async(self) -> str:
        """异步数据库URL"""
        if self.DATABASE_URL.startswith("sqlite"):
            return self.DATABASE_URL.replace("sqlite://", "sqlite+aiosqlite://")
        return self.DATABASE_URL


# 创建全局设置实例
settings = Settings()
