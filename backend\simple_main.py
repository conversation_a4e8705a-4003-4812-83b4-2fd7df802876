
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import json
from datetime import datetime

app = FastAPI(
    title="LLM任务分配系统API",
    description="无人机干扰决策系统后端API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "LLM任务分配系统API",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "backend",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/health")
async def api_health():
    return {
        "status": "healthy",
        "version": "1.0.0",
        "api": "v1",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/status")
async def api_status():
    return {
        "backend": "online",
        "database": "simulated",
        "llm": "not_configured",
        "timestamp": datetime.now().isoformat()
    }

# 模拟用户认证
@app.post("/api/v1/auth/login")
async def login(credentials: dict):
    return {
        "access_token": "demo_token",
        "token_type": "bearer",
        "user": {
            "id": 1,
            "username": "admin",
            "role": "admin"
        }
    }

# 模拟用户信息
@app.get("/api/v1/users/me")
async def get_current_user():
    return {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "role": "admin",
        "created_at": "2024-01-01T00:00:00"
    }

# 模拟场景列表
@app.get("/api/v1/scenarios")
async def get_scenarios():
    return {
        "scenarios": [
            {
                "id": 1,
                "name": "演示场景",
                "description": "系统演示场景",
                "created_at": "2024-01-01T00:00:00"
            }
        ],
        "total": 1
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
