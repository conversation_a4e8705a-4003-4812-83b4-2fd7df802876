from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from backend.core.config import settings
import logging

logger = logging.getLogger(__name__)

# 同步数据库引擎
engine = create_engine(
    settings.database_url_sync,
    echo=settings.DEBUG,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

# 异步数据库引擎
async_engine = create_async_engine(
    settings.database_url_async,
    echo=settings.DEBUG,
    future=True
)

# 会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=Session
)

AsyncSessionLocal = sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


def create_db_and_tables():
    """创建数据库表"""
    try:
        SQLModel.metadata.create_all(engine)
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"数据库表创建失败: {e}")
        raise


def get_session():
    """获取数据库会话"""
    with SessionLocal() as session:
        yield session


async def get_async_session():
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        yield session


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.async_engine = async_engine
    
    def get_session(self):
        """获取同步会话"""
        return SessionLocal()
    
    async def get_async_session(self):
        """获取异步会话"""
        return AsyncSessionLocal()
    
    def create_tables(self):
        """创建所有表"""
        SQLModel.metadata.create_all(self.engine)
    
    def drop_tables(self):
        """删除所有表"""
        SQLModel.metadata.drop_all(self.engine)
    
    def reset_database(self):
        """重置数据库"""
        self.drop_tables()
        self.create_tables()


# 全局数据库管理器实例
db_manager = DatabaseManager()
