import os
import json
import torch
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from transformers import (
    AutoTokenizer, AutoModel, AutoModelForCausalLM,
    AutoModelForSequenceClassification, pipeline,
    GPT2LMHeadModel, GPT2Tokenizer,
    BertModel, BertTokenizer,
    T5ForConditionalGeneration, T5Tokenizer
)
import logging
from datetime import datetime

from backend.core.config import settings
from backend.models.model_config import ModelConfig, ModelType, ModelStatus

logger = logging.getLogger(__name__)


class ModelManager:
    """大语言模型管理器"""
    
    def __init__(self):
        self.models: Dict[str, Any] = {}
        self.tokenizers: Dict[str, Any] = {}
        self.pipelines: Dict[str, Any] = {}
        self.model_configs: Dict[str, ModelConfig] = {}
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 确保模型缓存目录存在
        Path(settings.MODEL_CACHE_DIR).mkdir(parents=True, exist_ok=True)
        
        logger.info(f"ModelManager initialized with device: {self.device}")
    
    def load_model(self, model_config: ModelConfig) -> bool:
        """加载模型"""
        try:
            model_id = str(model_config.id)
            
            # 检查模型是否已加载
            if model_id in self.models:
                logger.info(f"Model {model_config.name} already loaded")
                return True
            
            # 根据模型类型加载不同的模型
            if model_config.model_type == ModelType.GPT:
                model, tokenizer = self._load_gpt_model(model_config)
            elif model_config.model_type == ModelType.BERT:
                model, tokenizer = self._load_bert_model(model_config)
            elif model_config.model_type == ModelType.T5:
                model, tokenizer = self._load_t5_model(model_config)
            elif model_config.model_type == ModelType.LLAMA:
                model, tokenizer = self._load_llama_model(model_config)
            else:
                model, tokenizer = self._load_custom_model(model_config)
            
            # 将模型移动到指定设备
            model = model.to(self.device)
            
            # 存储模型和分词器
            self.models[model_id] = model
            self.tokenizers[model_id] = tokenizer
            self.model_configs[model_id] = model_config
            
            # 创建推理管道
            self._create_pipeline(model_id, model_config)
            
            logger.info(f"Successfully loaded model: {model_config.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model {model_config.name}: {e}")
            return False
    
    def _load_gpt_model(self, config: ModelConfig) -> Tuple[Any, Any]:
        """加载GPT模型"""
        model_path = config.model_path or config.name
        
        if "gpt2" in model_path.lower():
            tokenizer = GPT2Tokenizer.from_pretrained(model_path)
            model = GPT2LMHeadModel.from_pretrained(model_path)
        else:
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForCausalLM.from_pretrained(model_path)
        
        # 设置pad_token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        return model, tokenizer
    
    def _load_bert_model(self, config: ModelConfig) -> Tuple[Any, Any]:
        """加载BERT模型"""
        model_path = config.model_path or config.name
        
        tokenizer = BertTokenizer.from_pretrained(model_path)
        model = BertModel.from_pretrained(model_path)
        
        return model, tokenizer
    
    def _load_t5_model(self, config: ModelConfig) -> Tuple[Any, Any]:
        """加载T5模型"""
        model_path = config.model_path or config.name
        
        tokenizer = T5Tokenizer.from_pretrained(model_path)
        model = T5ForConditionalGeneration.from_pretrained(model_path)
        
        return model, tokenizer
    
    def _load_llama_model(self, config: ModelConfig) -> Tuple[Any, Any]:
        """加载LLaMA模型"""
        model_path = config.model_path or config.name
        
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForCausalLM.from_pretrained(model_path)
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        return model, tokenizer
    
    def _load_custom_model(self, config: ModelConfig) -> Tuple[Any, Any]:
        """加载自定义模型"""
        model_path = config.model_path or config.name
        
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModel.from_pretrained(model_path)
        
        return model, tokenizer
    
    def _create_pipeline(self, model_id: str, config: ModelConfig):
        """创建推理管道"""
        try:
            model = self.models[model_id]
            tokenizer = self.tokenizers[model_id]
            
            if config.model_type in [ModelType.GPT, ModelType.LLAMA]:
                # 文本生成管道
                pipe = pipeline(
                    "text-generation",
                    model=model,
                    tokenizer=tokenizer,
                    device=0 if self.device.type == "cuda" else -1,
                    max_length=config.max_tokens,
                    temperature=config.temperature,
                    top_p=config.top_p,
                    top_k=config.top_k,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            elif config.model_type == ModelType.T5:
                # 文本到文本生成管道
                pipe = pipeline(
                    "text2text-generation",
                    model=model,
                    tokenizer=tokenizer,
                    device=0 if self.device.type == "cuda" else -1,
                    max_length=config.max_tokens,
                    temperature=config.temperature,
                    top_p=config.top_p,
                    top_k=config.top_k,
                    do_sample=True
                )
            else:
                # 特征提取管道
                pipe = pipeline(
                    "feature-extraction",
                    model=model,
                    tokenizer=tokenizer,
                    device=0 if self.device.type == "cuda" else -1
                )
            
            self.pipelines[model_id] = pipe
            
        except Exception as e:
            logger.error(f"Failed to create pipeline for model {model_id}: {e}")
    
    def unload_model(self, model_id: str) -> bool:
        """卸载模型"""
        try:
            if model_id in self.models:
                del self.models[model_id]
            if model_id in self.tokenizers:
                del self.tokenizers[model_id]
            if model_id in self.pipelines:
                del self.pipelines[model_id]
            if model_id in self.model_configs:
                del self.model_configs[model_id]
            
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            logger.info(f"Successfully unloaded model: {model_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload model {model_id}: {e}")
            return False
    
    def get_loaded_models(self) -> List[str]:
        """获取已加载的模型列表"""
        return list(self.models.keys())
    
    def is_model_loaded(self, model_id: str) -> bool:
        """检查模型是否已加载"""
        return model_id in self.models
    
    def get_model_info(self, model_id: str) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        if model_id not in self.model_configs:
            return None
        
        config = self.model_configs[model_id]
        model = self.models.get(model_id)
        
        info = {
            "id": model_id,
            "name": config.name,
            "type": config.model_type,
            "status": config.status,
            "loaded": model is not None,
            "device": str(self.device),
            "parameters": config.parameter_count,
            "size_gb": config.model_size
        }
        
        if model is not None:
            info["memory_usage"] = self._get_model_memory_usage(model)
        
        return info
    
    def _get_model_memory_usage(self, model) -> float:
        """获取模型内存使用量（MB）"""
        try:
            param_size = sum(p.numel() * p.element_size() for p in model.parameters())
            buffer_size = sum(b.numel() * b.element_size() for b in model.buffers())
            return (param_size + buffer_size) / (1024 * 1024)  # 转换为MB
        except:
            return 0.0


# 全局模型管理器实例
model_manager = ModelManager()
