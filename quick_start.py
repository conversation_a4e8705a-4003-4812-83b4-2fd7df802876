#!/usr/bin/env python3
"""
快速启动脚本 - 解决后端连接问题
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def install_packages():
    """安装必要包"""
    print("📦 安装必要包...")
    packages = ["fastapi", "uvicorn", "streamlit", "pandas", "requests", "plotly"]
    
    for package in packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
            print(f"✅ {package}")
        except:
            print(f"⚠️ {package} 安装失败")

def create_directories():
    """创建必要目录"""
    print("📁 创建目录...")
    for dir_name in ["backend", "data", "logs"]:
        Path(dir_name).mkdir(exist_ok=True)
    print("✅ 目录创建完成")

def start_backend():
    """启动后端"""
    print("🚀 启动后端...")
    
    # 检查后端文件
    backend_file = Path("backend/simple_app.py")
    if not backend_file.exists():
        print("❌ 后端文件不存在")
        return False
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                "cmd", "/c", "start", "后端服务", "cmd", "/k",
                f"{sys.executable} -m uvicorn backend.simple_app:app --host 0.0.0.0 --port 8000"
            ])
        else:  # Linux/Mac
            subprocess.Popen([
                sys.executable, "-m", "uvicorn", 
                "backend.simple_app:app", 
                "--host", "0.0.0.0", 
                "--port", "8000"
            ])
        
        print("✅ 后端启动命令已执行")
        return True
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return False

def start_frontend():
    """启动前端"""
    print("🚀 启动前端...")
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                "cmd", "/c", "start", "前端服务", "cmd", "/k",
                f"{sys.executable} -m streamlit run frontend/main.py --server.port 8501"
            ])
        else:  # Linux/Mac
            subprocess.Popen([
                sys.executable, "-m", "streamlit", "run",
                "frontend/main.py",
                "--server.port", "8501"
            ])
        
        print("✅ 前端启动命令已执行")
        return True
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🚁 LLM任务分配系统 - 快速启动")
    print("=" * 40)
    
    # 1. 安装包
    install_packages()
    print()
    
    # 2. 创建目录
    create_directories()
    print()
    
    # 3. 启动后端
    if start_backend():
        print("⏳ 等待后端启动...")
        time.sleep(5)
        print()
        
        # 4. 启动前端
        if start_frontend():
            print("🎉 启动完成!")
            print("📍 前端: http://localhost:8501")
            print("📍 后端: http://localhost:8000")
            print("📍 API文档: http://localhost:8000/docs")
            print("🔐 账号: admin / admin123")
            print()
            print("💡 如果看到'❌ 无法连接到后端服务'，请等待几秒钟后刷新页面")
        else:
            print("❌ 前端启动失败")
    else:
        print("❌ 后端启动失败")
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    main()
