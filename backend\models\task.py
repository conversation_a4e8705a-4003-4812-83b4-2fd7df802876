from sqlmodel import SQLModel, Field, Relationship, JSON, Column
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class DroneType(str, Enum):
    """无人机类型枚举"""
    FIXED_WING = "fixed_wing"
    ROTARY_WING = "rotary_wing"
    HYBRID = "hybrid"


class InterferenceMode(str, Enum):
    """干扰模式枚举"""
    JAMMING = "jamming"
    SPOOFING = "spoofing"
    DECEPTION = "deception"
    PHYSICAL = "physical"


class DroneConfig(SQLModel):
    """无人机配置模型"""
    id: str = Field(primary_key=True)
    name: str
    type: DroneType
    max_speed: float = Field(ge=0, default=50.0)  # 最大速度 m/s
    max_altitude: float = Field(ge=0, default=1000.0)  # 最大高度 m
    max_range: float = Field(ge=0, default=10000.0)  # 最大航程 m
    battery_capacity: float = Field(ge=0, default=100.0)  # 电池容量 %
    payload_capacity: float = Field(ge=0, default=5.0)  # 载荷容量 kg
    interference_capabilities: List[InterferenceMode] = Field(default_factory=list)
    sensors: List[str] = Field(default_factory=list)
    communication_range: float = Field(ge=0, default=5000.0)  # 通信距离 m


class Waypoint(SQLModel):
    """航点模型"""
    latitude: float = Field(ge=-90, le=90)
    longitude: float = Field(ge=-180, le=180)
    altitude: float = Field(ge=0, default=100.0)
    timestamp: Optional[datetime] = Field(default=None)
    action: Optional[str] = Field(default=None)  # 在此航点执行的动作
    parameters: Dict[str, Any] = Field(default_factory=dict)


class TrajectoryPlan(SQLModel):
    """轨迹规划模型"""
    drone_id: str
    waypoints: List[Waypoint]
    estimated_duration: float = Field(ge=0)  # 预计持续时间（秒）
    estimated_energy_consumption: float = Field(ge=0)  # 预计能耗
    interference_schedule: List[Dict[str, Any]] = Field(default_factory=list)
    risk_assessment: Dict[str, float] = Field(default_factory=dict)


class ObjectiveWeights(SQLModel):
    """目标权重模型"""
    interference_success_rate: float = Field(ge=0, le=1, default=0.3)
    energy_efficiency: float = Field(ge=0, le=1, default=0.25)
    regulatory_compliance: float = Field(ge=0, le=1, default=0.25)
    completion_time: float = Field(ge=0, le=1, default=0.2)


class TaskBase(SQLModel):
    """任务基础模型"""
    name: str = Field(min_length=1, max_length=100)
    description: Optional[str] = Field(default=None, max_length=500)
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    objective_weights: ObjectiveWeights = Field(default_factory=ObjectiveWeights, sa_column=Column(JSON))
    target_buoys: List[str] = Field(default_factory=list, sa_column=Column(JSON))  # 目标浮漂ID列表
    available_drones: List[DroneConfig] = Field(default_factory=list, sa_column=Column(JSON))
    constraints: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))


class Task(TaskBase, table=True):
    """任务数据库模型"""
    id: Optional[int] = Field(default=None, primary_key=True)
    creator_id: int = Field(foreign_key="user.id")
    scenario_id: int = Field(foreign_key="scenario.id")
    model_config_id: Optional[int] = Field(default=None, foreign_key="modelconfig.id")
    
    # 执行结果
    trajectory_plans: List[TrajectoryPlan] = Field(default_factory=list, sa_column=Column(JSON))
    execution_results: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))
    performance_metrics: Dict[str, float] = Field(default_factory=dict, sa_column=Column(JSON))
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    updated_at: Optional[datetime] = Field(default=None)
    
    # 关联关系
    creator: "User" = Relationship(back_populates="tasks")
    scenario: "Scenario" = Relationship(back_populates="tasks")
    model_config: Optional["ModelConfig"] = Relationship(back_populates="tasks")


class TaskCreate(TaskBase):
    """创建任务模型"""
    scenario_id: int
    model_config_id: Optional[int] = None


class TaskUpdate(SQLModel):
    """更新任务模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=100)
    description: Optional[str] = Field(default=None, max_length=500)
    priority: Optional[TaskPriority] = Field(default=None)
    status: Optional[TaskStatus] = Field(default=None)
    objective_weights: Optional[ObjectiveWeights] = Field(default=None)
    target_buoys: Optional[List[str]] = Field(default=None)
    available_drones: Optional[List[DroneConfig]] = Field(default=None)
    constraints: Optional[Dict[str, Any]] = Field(default=None)


class TaskRead(TaskBase):
    """读取任务模型"""
    id: int
    creator_id: int
    scenario_id: int
    model_config_id: Optional[int]
    trajectory_plans: List[TrajectoryPlan]
    execution_results: Dict[str, Any]
    performance_metrics: Dict[str, float]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    updated_at: Optional[datetime]


class TaskSummary(SQLModel):
    """任务摘要模型"""
    id: int
    name: str
    status: TaskStatus
    priority: TaskPriority
    drone_count: int
    target_count: int
    created_at: datetime
    completion_rate: Optional[float]


# 导入其他模型以避免循环导入
from backend.models.user import User
from backend.models.scenario import Scenario
from backend.models.model_config import ModelConfig
