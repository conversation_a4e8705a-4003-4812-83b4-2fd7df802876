import streamlit as st
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from frontend.pages import (
    login_page,
    model_config_page,
    scenario_design_page,
    simulation_page,
    user_profile_page,
    logs_page,
    data_management_page,
    help_page,
    settings_page,
    api_docs_page,
    security_page
)

# 页面配置
st.set_page_config(
    page_title="无人机干扰决策系统",
    page_icon="🚁",
    layout="wide",
    initial_sidebar_state="collapsed"
)


def main():
    """主应用函数"""
    # 全局CSS - 彻底隐藏白色框和多余元素
    st.markdown("""
    <style>
        /* 强制隐藏所有可能的白色框 */
        .stSelectbox, .stRadio, .stMultiSelect,
        [data-testid="stSelectbox"], [data-testid="stRadio"], [data-testid="stMultiSelect"],
        .stSelectbox > div, .stRadio > div,
        .element-container:has(.stSelectbox),
        .element-container:has(.stRadio) {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            position: absolute !important;
            left: -9999px !important;
            overflow: hidden !important;
        }

        /* 隐藏空的容器和可能的白色区域 */
        .block-container > div:empty,
        .main > div:empty,
        .element-container:empty,
        div[data-testid="stVerticalBlock"] > div:empty {
            display: none !important;
        }

        /* 确保正常组件显示 */
        .stTextInput, .stButton, .stForm, .stMarkdown {
            display: block !important;
            visibility: visible !important;
        }
        /* .stTabs, [data-testid="stTabs"], .stTabs > div, .element-container:has(.stTabs) 被注释，避免隐藏标签页 */
    </style>
    """, unsafe_allow_html=True)

    # 初始化会话状态
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'login'

    # 检查用户是否已登录
    if not st.session_state.authenticated:
        login_page.show()
        return

    # 显示主界面
    show_main_interface()


def show_main_interface():
    """显示主界面"""
    # 直接显示整合的主内容，不使用侧边栏
    show_integrated_main_content()


def show_integrated_main_content():
    """显示整合的主内容"""
    # 用户信息栏
    user = st.session_state.get('user', {})

    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem;
            border-radius: 10px;
            color: white;
            margin-bottom: 1rem;
        ">
            <h2 style="margin: 0;">🚁 无人机干扰决策系统</h2>
            <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">基于大语言模型的智能轨迹规划与干扰决策平台</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        # 用户信息卡片，点击可进入用户资料页面
        if st.button(f"""👤 {user.get('username', 'User')}
{user.get('role', 'user').title()}""", use_container_width=True):
            st.session_state.show_user_profile = True
            st.rerun()

    with col3:
        if st.button("🚪 退出登录", type="secondary", use_container_width=True, key="main_logout_btn"):
            st.session_state.authenticated = False
            st.session_state.current_page = 'login'
            # 清除用户数据
            if 'user' in st.session_state:
                del st.session_state['user']
            st.rerun()

    # 检查是否显示用户资料页面
    if st.session_state.get('show_user_profile', False):
        user_profile_page.show()
        if st.button("← 返回主页", key="back_to_main"):
            st.session_state.show_user_profile = False
            st.rerun()
        return

    # 检查是否显示设置页面
    if st.session_state.get('show_settings', False):
        settings_page.show()
        if st.button("← 返回主页", key="back_to_main_from_settings"):
            st.session_state.show_settings = False
            st.rerun()
        return

    # 主要功能区域 - 使用标签页整合所有功能
    user = st.session_state.get('user', {})
    is_admin = user.get('role') == 'admin'

    if is_admin:
        tab1, tab2, tab3, tab4, tab5, tab6, tab7, tab8, tab9 = st.tabs(["🤖 模型配置", "🗺️ 想定设计", "🎮 推演模块", "📊 系统状态", "📋 系统日志", "💾 数据管理", "🔧 API文档", "🔒 安全管理", "📚 帮助中心"])
    else:
        tab1, tab2, tab3, tab4, tab5 = st.tabs(["🤖 模型配置", "🗺️ 想定设计", "🎮 推演模块", "📊 系统状态", "📚 帮助中心"])

    with tab1:
        model_config_page.show()

    with tab2:
        scenario_design_page.show()

    with tab3:
        simulation_page.show()

    with tab4:
        show_system_status()

    # 帮助中心对所有用户开放
    if is_admin:
        with tab5:
            logs_page.show()

        with tab6:
            data_management_page.show()

        with tab7:
            api_docs_page.show()

        with tab8:
            security_page.show()

        with tab9:
            help_page.show()
    else:
        with tab5:
            help_page.show()


def show_system_status():
    """显示系统状态"""
    import requests
    from datetime import datetime

    st.markdown("### 📊 系统运行状态")

    # 系统资源监控
    col1, col2, col3, col4 = st.columns(4)

    # 尝试导入psutil，如果失败则显示简化版本
    try:
        import psutil

        with col1:
            cpu_percent = psutil.cpu_percent(interval=1)
            st.metric("CPU使用率", f"{cpu_percent:.1f}%",
                     delta=f"{'正常' if cpu_percent < 80 else '偏高'}")

        with col2:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            st.metric("内存使用率", f"{memory_percent:.1f}%",
                     delta=f"{'正常' if memory_percent < 80 else '偏高'}")

        with col3:
            try:
                import os
                if os.name == 'nt':
                    disk = psutil.disk_usage('C:/')
                else:
                    disk = psutil.disk_usage('/')
                disk_percent = disk.percent
            except Exception as e:
                disk_percent = 0  # 或者其他默认值
            st.metric("磁盘使用率", f"{disk_percent:.1f}%",
                     delta=f"{'正常' if disk_percent < 80 else '偏高'}")

    except ImportError:
        # 如果psutil不可用，显示简化信息
        with col1:
            st.metric("CPU使用率", "N/A", delta="需要安装psutil")

        with col2:
            st.metric("内存使用率", "N/A", delta="需要安装psutil")

        with col3:
            st.metric("磁盘使用率", "N/A", delta="需要安装psutil")

    with col4:
        # 检查后端服务状态
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            backend_status = "正常" if response.status_code == 200 else "异常"
        except:
            backend_status = "离线"

        st.metric("后端服务", backend_status)

    # 服务状态详情
    st.markdown("### 🔧 服务详情")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 前端服务")
        st.success("✅ Streamlit 服务运行正常")
        st.info(f"📍 地址: http://localhost:8501")
        st.info(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    with col2:
        st.markdown("#### 后端服务")
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                st.success("✅ FastAPI 服务运行正常")
                st.info(f"📍 地址: http://localhost:8000")
                st.info(f"📚 API文档: http://localhost:8000/docs")
            else:
                st.error("❌ 后端服务响应异常")
        except:
            st.error("❌ 无法连接到后端服务")

    # 数据库状态
    st.markdown("### 💾 数据库状态")

    from pathlib import Path
    import sqlite3

    db_path = Path("data/users.db")
    if db_path.exists():
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            conn.close()

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("用户总数", user_count)
            with col2:
                st.metric("数据库大小", f"{db_path.stat().st_size / 1024:.1f} KB")
            with col3:
                st.metric("数据库状态", "正常")

        except Exception as e:
            st.error(f"❌ 数据库连接失败: {e}")
    else:
        st.warning("⚠️ 用户数据库不存在")


if __name__ == "__main__":
    main()
