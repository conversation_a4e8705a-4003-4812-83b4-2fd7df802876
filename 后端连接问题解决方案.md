# 后端连接问题解决方案

## 问题描述
运行 `run.py` 时显示 "❌ 无法连接到后端服务"

## 原因分析
1. 后端服务未启动或启动失败
2. 端口8000被占用或无法访问
3. 缺少必要的依赖包（fastapi, uvicorn）
4. 后端文件路径或配置问题

## 解决方案

### 方案1：使用快速启动脚本（推荐）

1. **运行快速启动脚本**：
   ```bash
   python quick_start.py
   ```

2. **或者使用批处理文件（Windows）**：
   ```bash
   start.bat
   ```

### 方案2：手动启动（分步骤）

1. **安装依赖**：
   ```bash
   pip install fastapi uvicorn streamlit pandas requests plotly
   ```

2. **启动后端**（新开一个终端窗口）：
   ```bash
   python -m uvicorn backend.simple_app:app --host 0.0.0.0 --port 8000
   ```

3. **启动前端**（再开一个终端窗口）：
   ```bash
   streamlit run frontend/main.py --server.port 8501
   ```

### 方案3：测试后端连接

运行测试脚本检查后端状态：
```bash
python test_backend.py
```

## 验证步骤

1. **检查后端是否启动**：
   - 浏览器访问：http://localhost:8000
   - 应该看到：`{"message": "LLM任务分配系统API", "status": "running"}`

2. **检查健康状态**：
   - 浏览器访问：http://localhost:8000/health
   - 应该看到：`{"status": "healthy", "timestamp": "..."}`

3. **检查API文档**：
   - 浏览器访问：http://localhost:8000/docs
   - 应该看到FastAPI自动生成的API文档

4. **检查前端**：
   - 浏览器访问：http://localhost:8501
   - 应该看到登录页面，不再显示"无法连接到后端服务"

## 常见问题

### Q1: 端口8000被占用
**解决方法**：
```bash
# Windows
netstat -ano | findstr :8000
taskkill /F /PID <进程ID>

# Linux/Mac
lsof -ti:8000 | xargs kill -9
```

### Q2: 缺少依赖包
**解决方法**：
```bash
pip install fastapi uvicorn
```

### Q3: 后端启动失败
**检查方法**：
1. 确认 `backend/simple_app.py` 文件存在
2. 检查Python环境是否正确
3. 查看错误日志

### Q4: 前端仍显示连接错误
**解决方法**：
1. 等待10-15秒让后端完全启动
2. 刷新浏览器页面
3. 检查防火墙设置

## 文件说明

### 已创建的文件：
- `backend/simple_app.py` - 简化的后端服务
- `quick_start.py` - 快速启动脚本
- `start.bat` - Windows批处理启动脚本
- `test_backend.py` - 后端连接测试脚本
- `start_system.py` - 完整的系统启动脚本

### 后端API端点：
- `GET /` - 根路径，返回系统信息
- `GET /health` - 健康检查
- `GET /api/v1/health` - API健康检查
- `GET /api/v1/status` - 系统状态
- `POST /api/v1/auth/login` - 用户登录（模拟）
- `POST /api/v1/auth/register` - 用户注册（模拟）

## 推荐启动流程

1. **首次启动**：
   ```bash
   python quick_start.py
   ```

2. **日常使用**：
   - 方式1：双击 `start.bat`（Windows）
   - 方式2：运行 `python quick_start.py`
   - 方式3：手动分别启动后端和前端

3. **开发调试**：
   ```bash
   # 终端1：启动后端
   python -m uvicorn backend.simple_app:app --host 0.0.0.0 --port 8000 --reload
   
   # 终端2：启动前端
   streamlit run frontend/main.py --server.port 8501
   ```

## 注意事项

1. **启动顺序**：必须先启动后端，再启动前端
2. **等待时间**：后端启动需要5-10秒，请耐心等待
3. **端口占用**：确保8000和8501端口未被其他程序占用
4. **网络访问**：确保防火墙允许本地端口访问

## 成功标志

当看到以下信息时，说明系统启动成功：
- 后端：`INFO: Uvicorn running on http://0.0.0.0:8000`
- 前端：`You can now view your Streamlit app in your browser.`
- 前端页面不再显示"❌ 无法连接到后端服务"错误
