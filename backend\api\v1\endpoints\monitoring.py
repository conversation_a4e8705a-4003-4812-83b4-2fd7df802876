from fastapi import APIRouter, Depends
from backend.core.security import get_current_user
from backend.models.user import User
import psutil
import time

router = APIRouter()


@router.get("/system-status")
async def get_system_status(
    current_user: User = Depends(get_current_user)
):
    """获取系统状态"""
    try:
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 网络信息
        network = psutil.net_io_counters()
        
        return {
            "timestamp": time.time(),
            "cpu": {
                "usage_percent": cpu_percent,
                "count": psutil.cpu_count()
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percent": memory.percent
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            },
            "network": {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            }
        }
    except Exception as e:
        return {
            "error": str(e),
            "timestamp": time.time()
        }


@router.get("/tasks/{task_id}/metrics")
async def get_task_metrics(
    task_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取任务指标"""
    # 模拟任务指标数据
    return {
        "task_id": task_id,
        "metrics": {
            "execution_time": 1200.5,
            "success_rate": 0.85,
            "energy_consumption": 45.2,
            "completion_rate": 0.92
        },
        "timestamp": time.time()
    }


@router.get("/models/{model_id}/metrics")
async def get_model_metrics(
    model_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取模型指标"""
    # 模拟模型指标数据
    return {
        "model_id": model_id,
        "metrics": {
            "inference_time": 0.25,
            "accuracy": 0.89,
            "memory_usage": 2048.0,
            "gpu_utilization": 0.75
        },
        "timestamp": time.time()
    }
