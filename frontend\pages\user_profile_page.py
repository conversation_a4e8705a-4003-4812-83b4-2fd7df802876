import streamlit as st
import sqlite3
import hashlib
from pathlib import Path
from datetime import datetime


def get_user_info(user_id):
    """获取用户信息"""
    db_path = Path("data/users.db")
    if not db_path.exists():
        return None
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, username, email, role, created_at FROM users WHERE id = ?
    ''', (user_id,))
    
    user = cursor.fetchone()
    conn.close()
    
    if user:
        return {
            'id': user[0],
            'username': user[1],
            'email': user[2],
            'role': user[3],
            'created_at': user[4]
        }
    return None


def update_user_info(user_id, email):
    """更新用户信息"""
    db_path = Path("data/users.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            UPDATE users SET email = ? WHERE id = ?
        ''', (email, user_id))
        
        conn.commit()
        conn.close()
        return True
    except Exception:
        conn.close()
        return False


def change_password(user_id, old_password, new_password):
    """修改密码"""
    db_path = Path("data/users.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 验证旧密码
        old_password_hash = hashlib.sha256(old_password.encode()).hexdigest()
        cursor.execute('''
            SELECT id FROM users WHERE id = ? AND password_hash = ?
        ''', (user_id, old_password_hash))
        
        if not cursor.fetchone():
            conn.close()
            return False
        
        # 更新密码
        new_password_hash = hashlib.sha256(new_password.encode()).hexdigest()
        cursor.execute('''
            UPDATE users SET password_hash = ? WHERE id = ?
        ''', (new_password_hash, user_id))
        
        conn.commit()
        conn.close()
        return True
        
    except Exception:
        conn.close()
        return False


def show():
    """显示用户资料页面"""
    if not st.session_state.get('authenticated', False):
        st.error("请先登录")
        return
    
    user = st.session_state.get('user', {})
    user_info = get_user_info(user.get('id'))
    
    if not user_info:
        st.error("无法获取用户信息")
        return
    
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
    ">
        <h2 style="margin: 0;">👤 用户资料管理</h2>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">管理您的个人信息和账户设置</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 用户信息展示
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("### 📋 基本信息")
        
        with st.form("user_info_form"):
            st.text_input("👤 用户名", value=user_info['username'], disabled=True)
            email = st.text_input("📧 邮箱", value=user_info['email'] or "")
            st.text_input("🏷️ 角色", value=user_info['role'], disabled=True)
            st.text_input("📅 注册时间", value=user_info['created_at'], disabled=True)
            
            if st.form_submit_button("💾 保存信息", type="primary"):
                if update_user_info(user_info['id'], email):
                    st.success("✅ 信息更新成功！")
                    st.rerun()
                else:
                    st.error("❌ 信息更新失败")
    
    with col2:
        st.markdown("### 📊 账户统计")
        
        # 这里可以添加用户使用统计
        st.metric("账户状态", "正常", delta="活跃")
        st.metric("登录次数", "---", delta="本月")
        st.metric("使用天数", "---", delta="累计")
    
    # 密码修改
    st.markdown("### 🔒 修改密码")
    
    with st.form("change_password_form"):
        old_password = st.text_input("🔑 当前密码", type="password", placeholder="请输入当前密码")
        new_password = st.text_input("🔒 新密码", type="password", placeholder="请输入新密码（至少6位）")
        confirm_password = st.text_input("🔒 确认新密码", type="password", placeholder="请再次输入新密码")
        
        if st.form_submit_button("🔄 修改密码", type="secondary"):
            if not all([old_password, new_password, confirm_password]):
                st.error("⚠️ 请填写所有密码字段")
            elif len(new_password) < 6:
                st.error("⚠️ 新密码长度至少6位")
            elif new_password != confirm_password:
                st.error("⚠️ 两次输入的新密码不一致")
            else:
                if change_password(user_info['id'], old_password, new_password):
                    st.success("✅ 密码修改成功！")
                else:
                    st.error("❌ 密码修改失败，请检查当前密码是否正确")
    
    # 账户操作
    st.markdown("### ⚙️ 账户操作")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🔄 刷新信息", use_container_width=True, key="refresh_user_info"):
            st.rerun()

    with col2:
        if st.button("⚙️ 系统设置", use_container_width=True, key="user_profile_settings_btn"):
            st.session_state.show_settings = True
            st.rerun()

    with col3:
        if st.button("📤 导出数据", use_container_width=True, key="user_profile_export_btn"):
            st.success("✅ 数据导出功能已启用，您可以在数据管理页面进行详细操作")

    with col4:
        if st.button("🚪 退出登录", type="secondary", use_container_width=True, key="user_profile_logout_btn"):
            st.session_state.authenticated = False
            st.session_state.current_page = 'login'
            if 'user' in st.session_state:
                del st.session_state['user']
            st.rerun()
