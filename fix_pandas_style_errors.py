#!/usr/bin/env python3
"""
修复pandas样式错误脚本
解决 "argument of type 'float' is not iterable" 错误
"""

import re
from pathlib import Path

def fix_color_functions():
    """修复所有颜色函数"""
    print("🔧 修复pandas样式函数...")
    
    # 需要修复的文件
    files_to_fix = [
        "frontend/pages/security_page.py",
        "frontend/pages/logs_page.py", 
        "frontend/pages/api_docs_page.py",
        "frontend/pages/data_management_page.py"
    ]
    
    # 修复模式
    color_function_pattern = r'(def color_\w+\(val\):)\s*\n(\s+)(.*?return.*?)'
    
    safe_color_function_template = '''def {func_name}(val):
        # 确保val是字符串类型
        if not isinstance(val, str):
            val = str(val) if val is not None else ''
        
        {original_logic}'''
    
    for file_path in files_to_fix:
        file_obj = Path(file_path)
        if not file_obj.exists():
            print(f"⚠️ 文件不存在: {file_path}")
            continue
        
        try:
            with open(file_obj, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经修复
            if 'if not isinstance(val, str):' in content:
                print(f"✅ {file_path} 已经修复")
                continue
            
            # 查找并修复颜色函数
            modified = False
            lines = content.split('\n')
            new_lines = []
            i = 0
            
            while i < len(lines):
                line = lines[i]
                
                # 检查是否是颜色函数定义
                if re.match(r'\s*def color_\w+\(val\):', line):
                    print(f"🔧 修复函数: {line.strip()}")
                    
                    # 添加类型检查
                    indent = len(line) - len(line.lstrip())
                    new_lines.append(line)
                    new_lines.append(' ' * (indent + 4) + '# 确保val是字符串类型')
                    new_lines.append(' ' * (indent + 4) + 'if not isinstance(val, str):')
                    new_lines.append(' ' * (indent + 8) + "val = str(val) if val is not None else ''")
                    new_lines.append(' ' * (indent + 4) + '')
                    
                    modified = True
                else:
                    new_lines.append(line)
                
                i += 1
            
            if modified:
                with open(file_obj, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(new_lines))
                print(f"✅ {file_path} 修复完成")
            else:
                print(f"ℹ️ {file_path} 无需修复")
                
        except Exception as e:
            print(f"❌ 修复 {file_path} 失败: {e}")

def test_pandas_operations():
    """测试pandas操作"""
    print("🧪 测试pandas操作...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        test_data = {
            'status': ['success', 'error', None, np.nan, 'success'],
            'level': ['INFO', 'ERROR', None, np.nan, 'WARNING'],
            'method': ['GET', 'POST', None, np.nan, 'PUT']
        }
        
        df = pd.DataFrame(test_data)
        
        # 测试安全的颜色函数
        def safe_color_status(val):
            if not isinstance(val, str):
                val = str(val) if val is not None else ''
            
            if val == 'success':
                return 'background-color: #d4edda; color: #155724'
            elif val == 'error':
                return 'background-color: #f8d7da; color: #721c24'
            else:
                return ''
        
        # 应用样式
        styled_df = df.style.applymap(safe_color_status, subset=['status'])
        
        print("✅ pandas样式操作测试通过")
        return True
        
    except Exception as e:
        print(f"❌ pandas测试失败: {e}")
        return False

def create_safe_style_utils():
    """创建安全的样式工具函数"""
    print("🔧 创建安全样式工具...")
    
    utils_content = '''
"""
安全的pandas样式工具函数
防止 "argument of type 'float' is not iterable" 错误
"""

def safe_color_status(val):
    """安全的状态颜色函数"""
    if not isinstance(val, str):
        val = str(val) if val is not None else ''
    
    if '成功' in val or val == 'success':
        return 'background-color: #d4edda; color: #155724'
    elif '失败' in val or val == 'error':
        return 'background-color: #f8d7da; color: #721c24'
    else:
        return ''

def safe_color_level(val):
    """安全的级别颜色函数"""
    if not isinstance(val, str):
        val = str(val) if val is not None else ''
    
    colors = {
        'INFO': 'background-color: #d1ecf1; color: #0c5460',
        'WARNING': 'background-color: #fff3cd; color: #856404',
        'ERROR': 'background-color: #f8d7da; color: #721c24',
        'CRITICAL': 'background-color: #721c24; color: white'
    }
    return colors.get(val, '')

def safe_color_method(val):
    """安全的HTTP方法颜色函数"""
    if not isinstance(val, str):
        val = str(val) if val is not None else ''
    
    colors = {
        'GET': 'background-color: #d4edda; color: #155724',
        'POST': 'background-color: #cce5ff; color: #004085',
        'PUT': 'background-color: #fff3cd; color: #856404',
        'DELETE': 'background-color: #f8d7da; color: #721c24'
    }
    return colors.get(val, '')

def safe_color_severity(val):
    """安全的严重程度颜色函数"""
    if not isinstance(val, str):
        val = str(val) if val is not None else ''
    
    colors = {
        'LOW': 'background-color: #d1ecf1; color: #0c5460',
        'MEDIUM': 'background-color: #fff3cd; color: #856404',
        'HIGH': 'background-color: #f8d7da; color: #721c24',
        'CRITICAL': 'background-color: #721c24; color: white'
    }
    return colors.get(val, '')
'''
    
    utils_dir = Path("frontend/utils")
    utils_dir.mkdir(exist_ok=True)
    
    utils_file = utils_dir / "safe_styles.py"
    with open(utils_file, 'w', encoding='utf-8') as f:
        f.write(utils_content)
    
    print(f"✅ 安全样式工具已创建: {utils_file}")

def main():
    """主函数"""
    print("🔧 修复pandas样式错误")
    print("=" * 40)
    
    print("\n🚀 开始修复...")
    
    # 1. 修复现有的颜色函数
    fix_color_functions()
    
    # 2. 创建安全的样式工具
    create_safe_style_utils()
    
    # 3. 测试pandas操作
    if test_pandas_operations():
        print("\n✅ 修复完成!")
        print("💡 现在可以安全使用pandas样式功能")
    else:
        print("\n⚠️ 修复完成，但测试失败")
        print("💡 建议检查pandas版本")
    
    print("\n📝 修复说明:")
    print("   - 所有颜色函数现在都会检查数据类型")
    print("   - 非字符串值会被安全转换")
    print("   - 创建了安全的样式工具函数")

if __name__ == "__main__":
    main()
