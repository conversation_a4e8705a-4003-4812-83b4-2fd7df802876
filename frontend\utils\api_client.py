import requests
import streamlit as st
from typing import Optional, Dict, Any, List
import logging
import os

logger = logging.getLogger(__name__)


class APIClient:
    """API客户端"""
    
    def __init__(self):
        self.base_url = os.getenv("BACKEND_URL", "http://localhost:8000")
        self.api_base = f"{self.base_url}/api/v1"
        self.session = requests.Session()
        self.timeout = 30
        
        # 设置默认请求头
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })
    
    def set_auth_token(self, token: str):
        """设置认证令牌"""
        self.session.headers.update({
            "Authorization": f"Bearer {token}"
        })
    
    def clear_auth_token(self):
        """清除认证令牌"""
        if "Authorization" in self.session.headers:
            del self.session.headers["Authorization"]
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, params: Optional[Dict] = None) -> Optional[Dict]:
        """发起HTTP请求"""
        try:
            url = f"{self.api_base}{endpoint}"
            
            response = self.session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                timeout=self.timeout
            )
            
            # 检查响应状态
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 201:
                return response.json()
            elif response.status_code == 204:
                return {}
            elif response.status_code == 401:
                # 未授权，可能需要重新登录
                st.error("认证失效，请重新登录")
                # 清除认证状态
                if "authenticated" in st.session_state:
                    del st.session_state["authenticated"]
                return None
            elif response.status_code == 403:
                st.error("权限不足")
                return None
            elif response.status_code == 404:
                st.error("资源不存在")
                return None
            elif response.status_code == 422:
                # 验证错误
                error_detail = response.json().get("detail", "数据验证失败")
                if isinstance(error_detail, list):
                    error_messages = [f"{err.get('loc', [''])[-1]}: {err.get('msg', '')}" for err in error_detail]
                    st.error("数据验证失败:\n" + "\n".join(error_messages))
                else:
                    st.error(f"数据验证失败: {error_detail}")
                return None
            else:
                # 其他错误
                try:
                    error_detail = response.json().get("detail", f"请求失败 (状态码: {response.status_code})")
                    st.error(f"请求失败: {error_detail}")
                except:
                    st.error(f"请求失败 (状态码: {response.status_code})")
                return None
                
        except requests.exceptions.ConnectionError:
            st.error("无法连接到服务器，请检查网络连接")
            logger.error(f"Connection error for {method} {endpoint}")
            return None
        except requests.exceptions.Timeout:
            st.error("请求超时，请稍后重试")
            logger.error(f"Timeout error for {method} {endpoint}")
            return None
        except Exception as e:
            st.error(f"请求失败: {str(e)}")
            logger.error(f"Request error for {method} {endpoint}: {e}")
            return None
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """GET请求"""
        return self._make_request("GET", endpoint, params=params)
    
    def post(self, endpoint: str, data: Optional[Dict] = None) -> Optional[Dict]:
        """POST请求"""
        return self._make_request("POST", endpoint, data=data)
    
    def put(self, endpoint: str, data: Optional[Dict] = None) -> Optional[Dict]:
        """PUT请求"""
        return self._make_request("PUT", endpoint, data=data)
    
    def delete(self, endpoint: str) -> Optional[Dict]:
        """DELETE请求"""
        return self._make_request("DELETE", endpoint)
    
    def patch(self, endpoint: str, data: Optional[Dict] = None) -> Optional[Dict]:
        """PATCH请求"""
        return self._make_request("PATCH", endpoint, data=data)
    
    # 认证相关API
    def login(self, username: str, password: str) -> Optional[Dict]:
        """用户登录"""
        return self.post("/auth/login-json", {
            "username": username,
            "password": password
        })
    
    def register(self, user_data: Dict[str, Any]) -> Optional[Dict]:
        """用户注册"""
        return self.post("/auth/register", user_data)
    
    def logout(self) -> Optional[Dict]:
        """用户登出"""
        return self.post("/auth/logout")
    
    def refresh_token(self) -> Optional[Dict]:
        """刷新令牌"""
        return self.post("/auth/refresh")
    
    def verify_token(self) -> Optional[Dict]:
        """验证令牌"""
        return self.get("/auth/verify-token")
    
    # 用户相关API
    def get_current_user(self) -> Optional[Dict]:
        """获取当前用户信息"""
        return self.get("/users/me")
    
    def update_user_profile(self, user_data: Dict[str, Any]) -> Optional[Dict]:
        """更新用户资料"""
        return self.put("/users/me", user_data)
    
    def get_users(self, skip: int = 0, limit: int = 100) -> Optional[List[Dict]]:
        """获取用户列表"""
        response = self.get("/users/", params={"skip": skip, "limit": limit})
        return response if isinstance(response, list) else None
    
    # 场景相关API
    def get_scenarios(self, skip: int = 0, limit: int = 100) -> Optional[List[Dict]]:
        """获取场景列表"""
        response = self.get("/scenarios/", params={"skip": skip, "limit": limit})
        return response if isinstance(response, list) else None
    
    def create_scenario(self, scenario_data: Dict[str, Any]) -> Optional[Dict]:
        """创建场景"""
        return self.post("/scenarios/", scenario_data)
    
    def get_scenario(self, scenario_id: int) -> Optional[Dict]:
        """获取场景详情"""
        return self.get(f"/scenarios/{scenario_id}")
    
    def update_scenario(self, scenario_id: int, scenario_data: Dict[str, Any]) -> Optional[Dict]:
        """更新场景"""
        return self.put(f"/scenarios/{scenario_id}", scenario_data)
    
    def delete_scenario(self, scenario_id: int) -> Optional[Dict]:
        """删除场景"""
        return self.delete(f"/scenarios/{scenario_id}")
    
    # 任务相关API
    def get_tasks(self, skip: int = 0, limit: int = 100) -> Optional[List[Dict]]:
        """获取任务列表"""
        response = self.get("/tasks/", params={"skip": skip, "limit": limit})
        return response if isinstance(response, list) else None
    
    def create_task(self, task_data: Dict[str, Any]) -> Optional[Dict]:
        """创建任务"""
        return self.post("/tasks/", task_data)
    
    def get_task(self, task_id: int) -> Optional[Dict]:
        """获取任务详情"""
        return self.get(f"/tasks/{task_id}")
    
    def update_task(self, task_id: int, task_data: Dict[str, Any]) -> Optional[Dict]:
        """更新任务"""
        return self.put(f"/tasks/{task_id}", task_data)
    
    def delete_task(self, task_id: int) -> Optional[Dict]:
        """删除任务"""
        return self.delete(f"/tasks/{task_id}")
    
    def execute_task(self, task_id: int) -> Optional[Dict]:
        """执行任务"""
        return self.post(f"/tasks/{task_id}/execute")
    
    # 模型相关API
    def get_models(self, skip: int = 0, limit: int = 100) -> Optional[List[Dict]]:
        """获取模型列表"""
        response = self.get("/models/", params={"skip": skip, "limit": limit})
        return response if isinstance(response, list) else None
    
    def create_model(self, model_data: Dict[str, Any]) -> Optional[Dict]:
        """创建模型配置"""
        return self.post("/models/", model_data)
    
    def get_model(self, model_id: int) -> Optional[Dict]:
        """获取模型详情"""
        return self.get(f"/models/{model_id}")
    
    def update_model(self, model_id: int, model_data: Dict[str, Any]) -> Optional[Dict]:
        """更新模型配置"""
        return self.put(f"/models/{model_id}", model_data)
    
    def delete_model(self, model_id: int) -> Optional[Dict]:
        """删除模型配置"""
        return self.delete(f"/models/{model_id}")
    
    def load_model(self, model_id: int) -> Optional[Dict]:
        """加载模型"""
        return self.post(f"/models/{model_id}/load")
    
    def unload_model(self, model_id: int) -> Optional[Dict]:
        """卸载模型"""
        return self.post(f"/models/{model_id}/unload")
    
    # 监控相关API
    def get_system_status(self) -> Optional[Dict]:
        """获取系统状态"""
        return self.get("/monitoring/system-status")
    
    def get_task_metrics(self, task_id: int) -> Optional[Dict]:
        """获取任务指标"""
        return self.get(f"/monitoring/tasks/{task_id}/metrics")
    
    def get_model_metrics(self, model_id: int) -> Optional[Dict]:
        """获取模型指标"""
        return self.get(f"/monitoring/models/{model_id}/metrics")
