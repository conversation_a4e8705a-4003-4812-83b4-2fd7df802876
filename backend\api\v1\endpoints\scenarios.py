from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session, select

from backend.core.database import get_session
from backend.core.security import get_current_user
from backend.models.user import User
from backend.models.scenario import <PERSON><PERSON><PERSON>, ScenarioCreate, ScenarioUpdate, ScenarioRead, ScenarioSummary

router = APIRouter()


@router.get("/", response_model=List[ScenarioSummary])
async def get_scenarios(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """获取场景列表"""
    statement = select(Scenario).where(Scenario.owner_id == current_user.id).offset(skip).limit(limit)
    scenarios = session.exec(statement).all()
    
    # 转换为摘要格式
    summaries = []
    for scenario in scenarios:
        summary = ScenarioSummary(
            id=scenario.id,
            name=scenario.name,
            description=scenario.description,
            status=scenario.status,
            buoy_count=len(scenario.float_buoys),
            created_at=scenario.created_at,
            thumbnail_path=scenario.thumbnail_path
        )
        summaries.append(summary)
    
    return summaries


@router.post("/", response_model=ScenarioRead)
async def create_scenario(
    scenario_data: ScenarioCreate,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """创建场景"""
    scenario = Scenario(
        **scenario_data.dict(),
        owner_id=current_user.id
    )
    
    session.add(scenario)
    session.commit()
    session.refresh(scenario)
    
    return scenario


@router.get("/{scenario_id}", response_model=ScenarioRead)
async def get_scenario(
    scenario_id: int,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """获取场景详情"""
    statement = select(Scenario).where(
        Scenario.id == scenario_id,
        Scenario.owner_id == current_user.id
    )
    scenario = session.exec(statement).first()
    
    if not scenario:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scenario not found"
        )
    
    return scenario


@router.put("/{scenario_id}", response_model=ScenarioRead)
async def update_scenario(
    scenario_id: int,
    scenario_update: ScenarioUpdate,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """更新场景"""
    statement = select(Scenario).where(
        Scenario.id == scenario_id,
        Scenario.owner_id == current_user.id
    )
    scenario = session.exec(statement).first()
    
    if not scenario:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scenario not found"
        )
    
    scenario_data = scenario_update.dict(exclude_unset=True)
    for field, value in scenario_data.items():
        setattr(scenario, field, value)
    
    from datetime import datetime
    scenario.updated_at = datetime.utcnow()
    
    session.add(scenario)
    session.commit()
    session.refresh(scenario)
    
    return scenario


@router.delete("/{scenario_id}")
async def delete_scenario(
    scenario_id: int,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """删除场景"""
    statement = select(Scenario).where(
        Scenario.id == scenario_id,
        Scenario.owner_id == current_user.id
    )
    scenario = session.exec(statement).first()
    
    if not scenario:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scenario not found"
        )
    
    session.delete(scenario)
    session.commit()
    
    return {"message": "Scenario deleted successfully"}
