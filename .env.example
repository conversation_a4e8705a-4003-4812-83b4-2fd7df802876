# 数据库配置
DATABASE_URL=sqlite:///./data/app.db

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 环境配置
ENVIRONMENT=development
DEBUG=True

# API配置
API_V1_STR=/api/v1
PROJECT_NAME="LLM Task Allocation System"

# 大语言模型配置
DEFAULT_MODEL_NAME=gpt2
MODEL_CACHE_DIR=./models
MAX_MODEL_SIZE_GB=10

# 任务配置
MAX_CONCURRENT_TASKS=5
TASK_TIMEOUT_MINUTES=30

# 可视化配置
CESIUM_ACCESS_TOKEN=your-cesium-access-token
MAPBOX_ACCESS_TOKEN=your-mapbox-access-token

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# 文件上传配置
MAX_UPLOAD_SIZE_MB=100
UPLOAD_DIR=./data/uploads

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# 监控配置
ENABLE_METRICS=True
METRICS_PORT=9090

# 前端配置
FRONTEND_URL=http://localhost:8501
BACKEND_URL=http://localhost:8000

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100
