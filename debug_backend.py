#!/usr/bin/env python3
"""
后端调试脚本
用于诊断后端启动问题
"""

import sys
import traceback
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入"""
    print("🔍 测试导入...")
    
    try:
        print("  - 测试 fastapi...")
        from fastapi import FastAPI
        print("    ✅ FastAPI 导入成功")
    except ImportError as e:
        print(f"    ❌ FastAPI 导入失败: {e}")
        return False
    
    try:
        print("  - 测试 uvicorn...")
        import uvicorn
        print("    ✅ Uvicorn 导入成功")
    except ImportError as e:
        print(f"    ❌ Uvicorn 导入失败: {e}")
        return False
    
    try:
        print("  - 测试 backend.core.config...")
        from backend.core.config import settings
        print("    ✅ Config 导入成功")
    except Exception as e:
        print(f"    ❌ Config 导入失败: {e}")
        print(f"    详细错误: {traceback.format_exc()}")
        return False
    
    try:
        print("  - 测试 backend.core.database...")
        from backend.core.database import create_db_and_tables
        print("    ✅ Database 导入成功")
    except Exception as e:
        print(f"    ❌ Database 导入失败: {e}")
        print(f"    详细错误: {traceback.format_exc()}")
        return False
    
    return True

def test_simple_backend():
    """测试简单后端"""
    print("\n🚀 测试简单后端...")
    
    try:
        from fastapi import FastAPI
        import uvicorn
        
        app = FastAPI(title="Test Backend")
        
        @app.get("/")
        def read_root():
            return {"message": "Hello World"}
        
        @app.get("/health")
        def health_check():
            return {"status": "healthy"}
        
        print("    ✅ 简单后端创建成功")
        
        # 尝试启动（但不实际运行）
        print("    ✅ 后端配置正确")
        return True
        
    except Exception as e:
        print(f"    ❌ 简单后端创建失败: {e}")
        print(f"    详细错误: {traceback.format_exc()}")
        return False

def test_database_creation():
    """测试数据库创建"""
    print("\n💾 测试数据库创建...")
    
    try:
        # 创建数据目录
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        # 测试SQLite连接
        import sqlite3
        db_path = data_dir / "test.db"
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY)")
        conn.commit()
        conn.close()
        
        # 清理测试文件
        db_path.unlink()
        
        print("    ✅ 数据库连接正常")
        return True
        
    except Exception as e:
        print(f"    ❌ 数据库测试失败: {e}")
        return False

def create_minimal_backend():
    """创建最小化后端"""
    print("\n🔧 创建最小化后端...")
    
    minimal_backend = '''
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn
    
    print("✅ 导入成功")
    
    app = FastAPI(
        title="LLM任务分配系统",
        description="基于大语言模型的多目标任务分配与干扰决策系统",
        version="1.0.0"
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    async def root():
        return {
            "message": "LLM任务分配系统API",
            "version": "1.0.0",
            "status": "running"
        }
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy"}
    
    @app.get("/api/v1/auth/login-json")
    async def mock_login():
        return {
            "access_token": "demo_token",
            "token_type": "bearer",
            "expires_in": 3600,
            "user": {
                "id": 1,
                "username": "demo",
                "email": "<EMAIL>",
                "role": "user"
            }
        }
    
    @app.get("/api/v1/auth/verify-token")
    async def verify_token():
        return {"valid": True, "user_id": 1, "username": "demo"}
    
    print("✅ FastAPI应用创建成功")
    
    if __name__ == "__main__":
        print("🚀 启动后端服务...")
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )

except Exception as e:
    print(f"❌ 后端启动失败: {e}")
    import traceback
    print(f"详细错误: {traceback.format_exc()}")
    sys.exit(1)
'''
    
    backend_file = Path("minimal_backend.py")
    with open(backend_file, 'w', encoding='utf-8') as f:
        f.write(minimal_backend)
    
    print(f"    ✅ 创建最小化后端: {backend_file}")
    return backend_file

def main():
    """主函数"""
    print("🔍 LLM任务分配系统 - 后端调试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败")
        print("💡 建议:")
        print("   1. 安装缺失的包: pip install fastapi uvicorn")
        print("   2. 检查Python路径配置")
        return
    
    # 测试简单后端
    if not test_simple_backend():
        print("\n❌ 简单后端测试失败")
        return
    
    # 测试数据库
    test_database_creation()
    
    # 创建最小化后端
    backend_file = create_minimal_backend()
    
    print("\n" + "=" * 50)
    print("🎉 诊断完成!")
    print("\n📋 建议的解决方案:")
    print("1. 使用最小化后端:")
    print(f"   python {backend_file}")
    print("\n2. 或者修复原始后端的依赖问题")
    print("\n3. 检查错误日志获取更多信息")

if __name__ == "__main__":
    main()
