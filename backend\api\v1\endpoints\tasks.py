from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session, select

from backend.core.database import get_session
from backend.core.security import get_current_user
from backend.models.user import User
from backend.models.task import Task, TaskCreate, TaskUpdate, TaskRead, TaskSummary

router = APIRouter()


@router.get("/", response_model=List[TaskSummary])
async def get_tasks(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """获取任务列表"""
    statement = select(Task).where(Task.creator_id == current_user.id).offset(skip).limit(limit)
    tasks = session.exec(statement).all()
    
    # 转换为摘要格式
    summaries = []
    for task in tasks:
        summary = TaskSummary(
            id=task.id,
            name=task.name,
            status=task.status,
            priority=task.priority,
            drone_count=len(task.available_drones),
            target_count=len(task.target_buoys),
            created_at=task.created_at,
            completion_rate=task.performance_metrics.get('overall_score', 0.0) if task.performance_metrics else None
        )
        summaries.append(summary)
    
    return summaries


@router.post("/", response_model=TaskRead)
async def create_task(
    task_data: TaskCreate,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """创建任务"""
    task = Task(
        **task_data.dict(),
        creator_id=current_user.id
    )
    
    session.add(task)
    session.commit()
    session.refresh(task)
    
    return task


@router.get("/{task_id}", response_model=TaskRead)
async def get_task(
    task_id: int,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """获取任务详情"""
    statement = select(Task).where(
        Task.id == task_id,
        Task.creator_id == current_user.id
    )
    task = session.exec(statement).first()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    return task


@router.put("/{task_id}", response_model=TaskRead)
async def update_task(
    task_id: int,
    task_update: TaskUpdate,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """更新任务"""
    statement = select(Task).where(
        Task.id == task_id,
        Task.creator_id == current_user.id
    )
    task = session.exec(statement).first()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    task_data = task_update.dict(exclude_unset=True)
    for field, value in task_data.items():
        setattr(task, field, value)
    
    from datetime import datetime
    task.updated_at = datetime.utcnow()
    
    session.add(task)
    session.commit()
    session.refresh(task)
    
    return task


@router.delete("/{task_id}")
async def delete_task(
    task_id: int,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """删除任务"""
    statement = select(Task).where(
        Task.id == task_id,
        Task.creator_id == current_user.id
    )
    task = session.exec(statement).first()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    session.delete(task)
    session.commit()
    
    return {"message": "Task deleted successfully"}


@router.post("/{task_id}/execute")
async def execute_task(
    task_id: int,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """执行任务"""
    statement = select(Task).where(
        Task.id == task_id,
        Task.creator_id == current_user.id
    )
    task = session.exec(statement).first()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    # 这里应该调用任务执行逻辑
    # 暂时返回成功消息
    return {"message": "Task execution started", "task_id": task_id}
