import streamlit as st
import plotly.graph_objects as go
import numpy as np
import requests
import json
import random
from pathlib import Path


def load_model_config():
    """加载模型配置"""
    config_path = Path("data/model_config.json")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None


def load_scenarios():
    """加载所有想定"""
    scenarios_path = Path("data/scenarios")
    if not scenarios_path.exists():
        return []

    scenarios = []
    for scenario_file in scenarios_path.glob("*.json"):
        try:
            with open(scenario_file, 'r', encoding='utf-8') as f:
                scenarios.append(json.load(f))
        except:
            continue

    return sorted(scenarios, key=lambda x: x.get('created_at', ''), reverse=True)


def generate_situation_data(scenario):
    """生成态势信息，保证不低于70%的浮标处于通信状态"""
    # 解析地图大小
    map_size_raw = scenario['map_size']
    if isinstance(map_size_raw, str):
        if 'x' in map_size_raw:
            map_size = int(map_size_raw.split('x')[0])
        else:
            try:
                map_size = int(map_size_raw)
            except Exception:
                map_size = 10  # 默认值
    else:
        map_size = int(map_size_raw)

    # 生成无人机位置（原逻辑）
    drones = []
    for i in range(scenario['drones']):
        config = scenario['config']
        if config['drone_start_area'] == '西北角':
            x = random.uniform(0, map_size * 0.2)
            y = random.uniform(map_size * 0.8, map_size)
        elif config['drone_start_area'] == '东北角':
            x = random.uniform(map_size * 0.8, map_size)
            y = random.uniform(map_size * 0.8, map_size)
        elif config['drone_start_area'] == '西南角':
            x = random.uniform(0, map_size * 0.2)
            y = random.uniform(0, map_size * 0.2)
        elif config['drone_start_area'] == '东南角':
            x = random.uniform(map_size * 0.8, map_size)
            y = random.uniform(0, map_size * 0.2)
        else:  # 中心区域
            x = random.uniform(map_size * 0.4, map_size * 0.6)
            y = random.uniform(map_size * 0.4, map_size * 0.6)
        drones.append({
            'id': f'drone_{i+1}',
            'x': round(x, 2),
            'y': round(y, 2),
            'z': config['flight_altitude'],
            'status': 'active'
        })

    # 生成浮标，保证70%处于通信状态
    buoys = []
    n_buoy = scenario['buoys']
    comm_range = 15  # 通信范围，可从配置获取
    min_connected = int(n_buoy * 0.7)
    placed = 0
    # 先放置保证通信的浮标
    while placed < min_connected:
        if placed == 0:
            # 第一个浮标随机放
            x = random.uniform(0, map_size)
            y = random.uniform(0, map_size)
        else:
            # 后续浮标放在已有浮标通信范围内
            base = random.choice(buoys)
            angle = random.uniform(0, 2 * np.pi)
            r = random.uniform(1, comm_range * 0.9)
            x = min(max(base['x'] + r * np.cos(angle), 0), map_size)
            y = min(max(base['y'] + r * np.sin(angle), 0), map_size)
        buoys.append({
            'id': f'buoy_{placed+1}',
            'x': round(x, 2),
            'y': round(y, 2),
            'signal_strength': config['signal_strength'],
            'detection_range': config['detection_range']
        })
        placed += 1
    # 剩余浮标随机放
    for i in range(placed, n_buoy):
        x = random.uniform(0, map_size)
        y = random.uniform(0, map_size)
        buoys.append({
            'id': f'buoy_{i+1}',
            'x': round(x, 2),
            'y': round(y, 2),
            'signal_strength': config['signal_strength'],
            'detection_range': config['detection_range']
        })

    return {
        'scenario_name': scenario['name'],
        'map_size': map_size,
        'drones': drones,
        'buoys': buoys,
        'interference_config': {
            'types': config['interference_types'],
            'strength': config['interference_strength'],
            'range': config['interference_range']
        }
    }


def call_llm_for_planning(situation_data):
    """调用大模型进行轨迹规划，自动修正AI输出为标准JSON"""
    config = load_model_config()
    if not config:
        return None, "未找到模型配置"
    
    # 选择可用的模型
    model_config = None
    if config['local_model']['enabled']:
        model_config = config['local_model']
        api_url = f"{str(model_config['api_url']).strip()}/v1/chat/completions"
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Accept": "application/json"
        }
    elif config['remote_model']['enabled']:
        model_config = config['remote_model']
        api_url = f"{str(model_config['api_url']).strip()}/v1/chat/completions"
        api_key = str(model_config['api_key']).strip()
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Accept": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
    else:
        return None, "没有可用的模型配置"

    # 生成干扰-通信影响矩阵（值为20-100）
    interference_modes = situation_data['interference_config']['types']
    communication_modes = situation_data.get('communication_modes')
    if not communication_modes:
        communication_modes = 3
        if 'config' in situation_data:
            communication_modes = situation_data['config'].get('communication_modes', 3)
    comm_mode_names = [f"通信模式{i+1}" for i in range(communication_modes)]
    import numpy as np
    np.random.seed()
    matrix = np.random.uniform(20, 100, (len(interference_modes), communication_modes))
    matrix = np.round(matrix, 2)
    matrix_list = matrix.tolist()
    matrix_json = {
        'interference_modes': interference_modes,
        'communication_modes': comm_mode_names,
        'matrix': matrix_list
    }

    # 自动生成浮标通信网络邻接矩阵（每个浮标一对一通信）
    buoys = situation_data['buoys']
    n_buoy = len(buoys)
    adj_matrix = [[0]*n_buoy for _ in range(n_buoy)]
    for i in range(n_buoy):
        j = (i+1)%n_buoy
        adj_matrix[i][j] = 1
    buoy_network = {
        'buoy_ids': [buoy['id'] for buoy in buoys],
        'adjacency_matrix': adj_matrix
    }

    # 定制prompt
    prompt = f"""
你是多目标任务分配专家。请根据以下规则和数据，为每个无人机规划最佳的干扰位置和干扰模式。

【坐标系说明】
- 地图原点为左下角(0,0)，单位为km，地图范围为0~{situation_data['map_size']}。
- 所有坐标均为地图内实际位置。

【浮标通信规则】
- 同一时刻每个浮标只与一个浮标通信，通信对为一对一。
- 若通信，通信质量为100。
- 通信不受距离、通信模式等影响。

【干扰规则】
- 只有在无人机干扰范围内的浮标才会被干扰。
- 干扰-通信影响矩阵的值为20-100，表示不同干扰模式对不同通信模式的影响。
- 若浮标未被干扰，通信质量为100。
- 若浮标被干扰，通信质量=100-影响值（影响值由无人机选择的干扰模式和浮标通信模式决定，查表获得）。
- 干扰只影响被覆盖的浮标。

【目标】
- 你的目标是：为每架无人机选择最佳的干扰模式和目的地坐标，使所有浮标的通信质量总和最小化。
- 你必须为每架无人机分配唯一的目标点（目标点数量与无人机数量一致且不能重复）。
- 目标点必须在地图范围内。
- 请严格基于上述数据做出最优决策，不要随意编造。

【态势信息】
- 地图大小：{situation_data['map_size']}x{situation_data['map_size']} km
- 无人机数量：{len(situation_data['drones'])}
- 浮标数量：{len(situation_data['buoys'])}

【无人机当前位置】
{json.dumps(situation_data['drones'], indent=2, ensure_ascii=False)}

【浮标位置】
{json.dumps(situation_data['buoys'], indent=2, ensure_ascii=False)}

【浮标通信网络邻接矩阵】
- buoy_ids: {json.dumps(buoy_network['buoy_ids'], ensure_ascii=False)}
- adjacency_matrix: 行列顺序与buoy_ids一致，1表示两浮标通信，0为不可通信。
{json.dumps(buoy_network['adjacency_matrix'], indent=2, ensure_ascii=False)}

【干扰配置】
{json.dumps(situation_data['interference_config'], indent=2, ensure_ascii=False)}

【干扰-通信影响矩阵】
{json.dumps(matrix_json, indent=2, ensure_ascii=False)}

【输出格式】
请严格按照以下JSON格式返回结果：
{{
  "planning_result": [
    {{
      "drone_id": "drone_1",
      "target_position": {{
        "x": 坐标值,
        "y": 坐标值,
        "z": 坐标值
      }},
      "interference_mode": "干扰模式名称",
      "expected_effect": "预计效果描述",
      "priority": "高/中/低"
    }}
  ],
  "overall_strategy": "整体策略描述"
}}
"""

    prompt += '''
【输出要求】
- 请严格只输出JSON，不要输出任何解释、注释或多余内容。
- JSON必须以 { 开头，以 } 结尾，且符合标准JSON语法。
- 如果无法输出标准JSON，请只返回 {}。
'''

    try:
        # 确保所有字符串参数都正确编码
        model_name = str(model_config['model_name']).strip()
        data = {
            "model": model_name,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": model_config['max_tokens'],
            "temperature": model_config['temperature']
        }
        response = requests.post(api_url, headers=headers, json=data, timeout=2000)
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            try:
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                json_str = content[start_idx:end_idx]
                planning_result = json.loads(json_str)
                # 增加健壮性校验，确保planning_result字段存在
                if 'planning_result' not in planning_result:
                    planning_result = {'planning_result': [], 'overall_strategy': ''}
                return planning_result, None
            except:
                # 自动修正：将原始内容再发给AI，请其只输出标准JSON
                fix_prompt = f"请将下面的内容修正为标准JSON格式，只输出JSON，不要有任何解释或注释：\n\n{content}"
                fix_data = {
                    "model": model_name,  # 使用已处理的model_name
                    "messages": [{"role": "user", "content": fix_prompt}],
                    "max_tokens": model_config['max_tokens'],
                    "temperature": 0
                }
                fix_response = requests.post(api_url, headers=headers, json=fix_data, timeout=2000)
                if fix_response.status_code == 200:
                    fix_result = fix_response.json()
                    fix_content = fix_result['choices'][0]['message']['content']
                    try:
                        fix_start = fix_content.find('{')
                        fix_end = fix_content.rfind('}') + 1
                        fix_json_str = fix_content[fix_start:fix_end]
                        planning_result = json.loads(fix_json_str)
                        if 'planning_result' not in planning_result:
                            planning_result = {'planning_result': [], 'overall_strategy': ''}
                        return planning_result, None
                    except:
                        return {"planning_result": [], "overall_strategy": "", "raw_response": fix_content}, None
                else:
                    return {"planning_result": [], "overall_strategy": "", "raw_response": content}, None
        else:
            # 确保响应文本使用UTF-8解码
            response.encoding = 'utf-8'
            return None, f"API调用失败: {response.status_code} - {response.text}"
    except UnicodeEncodeError as e:
        return None, f"字符编码错误: {str(e)} - 请检查API密钥或模型名称是否包含特殊字符"
    except Exception as e:
        return None, f"调用模型时出错: {str(e)}"


def show():
    """显示推演页面"""
    
    # 页面样式
    st.markdown("""
    <style>
        .simulation-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
            border-radius: 15px;
            color: white;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }
        
        .control-panel {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            margin-bottom: 1rem;
        }
        
        .status-success {
            background: #dcfce7;
            color: #166534;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: 1px solid #bbf7d0;
            margin: 0.5rem 0;
        }
        
        .status-error {
            background: #fee2e2;
            color: #991b1b;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: 1px solid #fecaca;
            margin: 0.5rem 0;
        }
    </style>
    """, unsafe_allow_html=True)

    # 设置网页背景图片
    st.markdown(
        f"""
        <style>
        body {{
            background-image: url('file:///C:/Users/<USER>/Desktop/llm_task_allocation_system%20-%20%E5%89%AF%E6%9C%AC/11.jpg');
            background-size: cover;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }}
        </style>
        """,
        unsafe_allow_html=True
    )

    # 简化的说明
    st.info("💡 选择想定进行推演，生成态势并让AI进行轨迹规划")

    # 想定选择
    scenarios = load_scenarios()
    if not scenarios:
        st.warning("⚠️ 暂无想定，请先在想定设计页面创建想定")
        return

    # 想定选择器
    scenario_names = [s['name'] for s in scenarios]

    # 检查是否有预选的想定
    preselected_scenario = st.session_state.get('simulation_scenario')
    default_index = 0
    if preselected_scenario:
        try:
            default_index = scenario_names.index(preselected_scenario['name'])
            st.info(f"🎯 已自动选择想定：{preselected_scenario['name']}")
        except ValueError:
            pass

    selected_scenario_name = st.selectbox(
        "选择想定",
        scenario_names,
        index=default_index,
        key="simulation_scenario_selector"
    )

    # 获取选中的想定
    scenario = next((s for s in scenarios if s['name'] == selected_scenario_name), None)
    if not scenario:
        st.error("❌ 未找到选中的想定")
        return

    st.success(f"✅ 当前想定: {scenario['name']}")
    
    # 控制面板
    st.markdown('<div class="control-panel">', unsafe_allow_html=True)
    st.markdown("### 🎛️ 控制面板")

    # 第一行：主要操作
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🎲 生成态势", type="primary", use_container_width=True):
            situation_data = generate_situation_data(scenario)
            if situation_data is None:
                return
            st.session_state.situation_data = situation_data
            st.success("✅ 态势信息已生成")
            st.rerun()

    with col2:
        if st.button("🤖 AI规划", type="primary", use_container_width=True):
            if 'situation_data' not in st.session_state:
                st.error("⚠️ 请先生成态势信息")
            else:
                with st.spinner("🤖 AI正在分析态势并规划轨迹..."):
                    planning_result, error = call_llm_for_planning(st.session_state.situation_data)
                    if error:
                        st.error(f"❌ AI规划失败: {error}")
                    else:
                        st.session_state.planning_result = planning_result
                        st.session_state.show_trajectory = True  # 自动显示轨迹
                        st.success("✅ AI规划完成")
                        st.rerun()

    with col3:
        trajectory_visible = st.session_state.get('show_trajectory', False)
        if st.button("👁️ 隐藏轨迹" if trajectory_visible else "📊 显示轨迹", use_container_width=True):
            if 'planning_result' not in st.session_state:
                st.error("⚠️ 请先进行AI规划")
            else:
                st.session_state.show_trajectory = not trajectory_visible
                st.rerun()

    with col4:
        if st.button("🔄 重置", use_container_width=True):
            # 清除所有推演数据
            for key in ['situation_data', 'planning_result', 'show_trajectory']:
                if key in st.session_state:
                    del st.session_state[key]
            st.success("✅ 推演数据已重置")
            st.rerun()

    # 第二行：状态信息
    if 'situation_data' in st.session_state:
        situation_data = st.session_state.situation_data
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("🚁 无人机", f"{len(situation_data['drones'])}架")

        with col2:
            st.metric("📍 浮标", f"{len(situation_data['buoys'])}个")

        with col3:
            planning_status = "已完成" if 'planning_result' in st.session_state else "未开始"
            st.metric("🤖 AI规划", planning_status)

        with col4:
            trajectory_status = "显示中" if st.session_state.get('show_trajectory', False) else "隐藏"
            st.metric("📊 轨迹显示", trajectory_status)

    st.markdown('</div>', unsafe_allow_html=True)
    
    # 显示可视化
    if 'situation_data' in st.session_state:
        show_3d_visualization()

        # # 添加动画控制
        # if 'planning_result' in st.session_state and st.session_state.get('show_trajectory', False):
        #     st.markdown("### 🎬 动画控制")

        #     col1, col2, col3 = st.columns([1, 2, 1])

        #     with col1:
        #         if st.button("▶️ 播放动画", use_container_width=True):
        #             show_trajectory_animation()

        #     with col2:
        #         animation_speed = st.slider("动画速度", 0.1, 2.0, 1.0, 0.1)
        #         st.session_state.animation_speed = animation_speed

        #     with col3:
        #         if st.button("⏹️ 停止动画", use_container_width=True):
        #             st.session_state.animation_playing = False

    # 显示态势信息和规划结果
    if 'situation_data' in st.session_state:
        col1, col2 = st.columns([1, 1])

        with col1:
            show_situation_info()

        with col2:
            if 'planning_result' in st.session_state:
                show_planning_results()


def show_3d_visualization():
    """显示2D可视化"""
    st.markdown("### 🗺️ 态势显示")

    situation_data = st.session_state.situation_data

    # 只显示2D俯视图
    show_2d_map(situation_data)


def show_2d_map(situation_data):
    """显示2D俯视图"""
    fig = go.Figure()

    # 添加地图背景网格
    map_size = situation_data['map_size']

    # 创建网格线
    for i in range(0, map_size + 1, max(1, map_size // 10)):
        # 垂直线
        fig.add_trace(go.Scatter(
            x=[i, i], y=[0, map_size],
            mode='lines',
            line=dict(color='lightgray', width=1),
            showlegend=False,
            hoverinfo='skip'
        ))
        # 水平线
        fig.add_trace(go.Scatter(
            x=[0, map_size], y=[i, i],
            mode='lines',
            line=dict(color='lightgray', width=1),
            showlegend=False,
            hoverinfo='skip'
        ))

    # 添加浮标通信连接 - 改进算法，确保每个浮标只与一个浮标通信
    buoys = situation_data['buoys']
    communication_range = 15  # 通信范围，可以从配置中获取

    # 为每个浮标找到最近的通信伙伴
    connected_pairs = set()

    for i, buoy1 in enumerate(buoys):
        if any(i in pair for pair in connected_pairs):
            continue  # 如果已经连接，跳过

        min_distance = float('inf')
        best_partner = None
        best_j = None

        for j, buoy2 in enumerate(buoys):
            if i == j or any(j in pair for pair in connected_pairs):
                continue

            distance = np.sqrt((buoy1['x'] - buoy2['x'])**2 + (buoy1['y'] - buoy2['y'])**2)
            if distance <= communication_range and distance < min_distance:
                min_distance = distance
                best_partner = buoy2
                best_j = j

        if best_partner:
            connected_pairs.add((i, best_j))
            # 添加通信连接线
            fig.add_trace(go.Scatter(
                x=[buoy1['x'], best_partner['x']],
                y=[buoy1['y'], best_partner['y']],
                mode='lines',
                line=dict(color='cyan', width=3, dash='solid'),
                showlegend=False,
                hoverinfo='skip',
                name='通信连接'
            ))

    # 添加浮标
    buoy_x = [buoy['x'] for buoy in situation_data['buoys']]
    buoy_y = [buoy['y'] for buoy in situation_data['buoys']]
    # 不显示浮标id
    fig.add_trace(go.Scatter(
        x=buoy_x, y=buoy_y,
        mode='markers',
        marker=dict(size=15, color='red', symbol='circle', line=dict(width=2, color='darkred')),
        name='浮标',
        hovertemplate='X: %{x:.1f}km<br>Y: %{y:.1f}km<extra></extra>'
    ))



    # 添加无人机干扰范围 - 每个无人机显示一个圆形干扰区域
    interference_range = situation_data['interference_config']['range']
    colors = ['orange', 'purple', 'brown', 'pink', 'gray']

    for i, drone in enumerate(situation_data['drones']):
        theta = np.linspace(0, 2*np.pi, 100)  # 更多点使圆形更平滑
        circle_x = drone['x'] + interference_range * np.cos(theta)
        circle_y = drone['y'] + interference_range * np.sin(theta)

        color = colors[i % len(colors)]

        fig.add_trace(go.Scatter(
            x=circle_x, y=circle_y,
            mode='lines',
            line=dict(color=color, width=3, dash='solid'),
            fill='toself',
            fillcolor=f'rgba({255 if color=="orange" else 128},{165 if color=="orange" else 0},{0},{0.15})',
            showlegend=True,
            name=f'{drone["id"]} 干扰范围',
            hovertemplate=f'<b>{drone["id"]} 干扰范围</b><br>半径: {interference_range}km<extra></extra>'
        ))

    # 添加无人机当前位置
    drone_x = [drone['x'] for drone in situation_data['drones']]
    drone_y = [drone['y'] for drone in situation_data['drones']]
    drone_names = [drone['id'] for drone in situation_data['drones']]

    fig.add_trace(go.Scatter(
        x=drone_x, y=drone_y,
        mode='markers+text',
        marker=dict(size=12, color='blue', symbol='triangle-up', line=dict(width=2, color='darkblue')),
        text=drone_names,
        textposition="top center",
        name='无人机',
        hovertemplate='<b>%{text}</b><br>X: %{x:.1f}km<br>Y: %{y:.1f}km<extra></extra>'
    ))

    # 添加规划轨迹
    if 'planning_result' in st.session_state and st.session_state.get('show_trajectory', False):
        planning_result = st.session_state.planning_result

        # 检查规划结果的格式
        if isinstance(planning_result, dict):
            if 'planning_result' in planning_result:
                # 正常的JSON格式
                colors = ['green', 'orange', 'purple', 'brown', 'pink']
                for i, plan in enumerate(planning_result['planning_result']):
                    try:
                        drone_id = plan['drone_id']
                        target_pos = plan['target_position']
                        color = colors[i % len(colors)]

                        # 找到对应的无人机当前位置
                        current_drone = next((d for d in situation_data['drones'] if d['id'] == drone_id), None)
                        if current_drone:
                            # 生成平滑轨迹
                            trajectory_x, trajectory_y = generate_smooth_trajectory(
                                current_drone['x'], current_drone['y'],
                                target_pos['x'], target_pos['y']
                            )

                            # 添加轨迹线
                            fig.add_trace(go.Scatter(
                                x=trajectory_x, y=trajectory_y,
                                mode='lines+markers',
                                line=dict(color=color, width=3),
                                marker=dict(size=4, color=color),
                                name=f'{drone_id}_轨迹',
                                hovertemplate=f'<b>{drone_id} 轨迹</b><br>X: %{{x:.1f}}<br>Y: %{{y:.1f}}<extra></extra>'
                            ))

                            # 添加目标位置标记
                            fig.add_trace(go.Scatter(
                                x=[target_pos['x']], y=[target_pos['y']],
                                mode='markers',
                                marker=dict(size=12, color=color, symbol='star', line=dict(width=2, color='white')),
                                name=f'{drone_id}_目标',
                                hovertemplate=f'<b>{drone_id} 目标位置</b><br>X: %{{x:.1f}}<br>Y: %{{y:.1f}}<extra></extra>'
                            ))

                            # 在目标点画干扰范围圆
                            theta = np.linspace(0, 2*np.pi, 100)
                            interference_range = situation_data['interference_config']['range']
                            circle_x = target_pos['x'] + interference_range * np.cos(theta)
                            circle_y = target_pos['y'] + interference_range * np.sin(theta)
                            fig.add_trace(go.Scatter(
                                x=circle_x, y=circle_y,
                                mode='lines',
                                line=dict(color=color, width=2, dash='dot'),
                                fill='toself',
                                fillcolor=f'rgba(0,0,0,0.08)',
                                showlegend=False,
                                name=f'{drone_id} 干扰范围(终点)',
                                hovertemplate=f'<b>{drone_id} 干扰范围(终点)</b><br>半径: {interference_range}km<extra></extra>'
                            ))
                    except Exception as e:
                        print(f"处理轨迹 {i} 时出错: {e}")
                        continue
            else:
                # 如果没有正确的规划结果，生成示例轨迹
                colors = ['green', 'orange', 'purple', 'brown', 'pink']
                for i, drone in enumerate(situation_data['drones']):
                    color = colors[i % len(colors)]
                    # 生成一个示例目标位置
                    target_x = min(max(drone['x'] + random.uniform(-20, 20), 0), situation_data['map_size'])
                    target_y = min(max(drone['y'] + random.uniform(-20, 20), 0), situation_data['map_size'])

                    # 生成轨迹
                    trajectory_x, trajectory_y = generate_smooth_trajectory(
                        drone['x'], drone['y'], target_x, target_y
                    )

                    # 添加轨迹线
                    fig.add_trace(go.Scatter(
                        x=trajectory_x, y=trajectory_y,
                        mode='lines+markers',
                        line=dict(color=color, width=3),
                        marker=dict(size=4, color=color),
                        name=f'{drone["id"]}_轨迹',
                        hovertemplate=f'<b>{drone["id"]} 轨迹</b><br>X: %{{x:.1f}}<br>Y: %{{y:.1f}}<extra></extra>'
                    ))

                    # 添加目标位置
                    fig.add_trace(go.Scatter(
                        x=[target_x], y=[target_y],
                        mode='markers+text',
                        marker=dict(size=15, color=color, symbol='star', line=dict(width=2, color='black')),
                        text=[f"{drone['id']}_目标"],
                        textposition="bottom center",
                        name=f'{drone["id"]}_目标',
                        showlegend=False,
                        hovertemplate=f'<b>{drone["id"]} 目标位置</b><br>X: %{{x:.1f}}<br>Y: %{{y:.1f}}<extra></extra>'
                    ))

                    # 在目标点画虚线干扰范围圆
                    theta = np.linspace(0, 2*np.pi, 100)
                    interference_range = situation_data['interference_config']['range']
                    circle_x = target_x + interference_range * np.cos(theta)
                    circle_y = target_y + interference_range * np.sin(theta)
                    fig.add_trace(go.Scatter(
                        x=circle_x, y=circle_y,
                        mode='lines',
                        line=dict(color=color, width=2, dash='dot'),
                        fill='toself',
                        fillcolor=f'rgba(0,0,0,0.08)',
                        showlegend=False,
                        name=f'{drone["id"]} 干扰范围(终点)',
                        hovertemplate=f'<b>{drone["id"]} 干扰范围(终点)</b><br>半径: {interference_range}km<extra></extra>'
                    ))

    # 更新布局，支持缩放和平移
    fig.update_layout(
        title="2D态势图 - 无人机轨迹规划",
        xaxis_title='X坐标 (km)',
        yaxis_title='Y坐标 (km)',
        xaxis=dict(
            range=[0, map_size],
            constrain='domain',
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray'
        ),
        yaxis=dict(
            range=[0, map_size],
            scaleanchor="x",
            scaleratio=1,
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray'
        ),
        height=600,  # 增加高度
        showlegend=True,
        legend=dict(x=1.02, y=1),
        margin=dict(l=0, r=0, b=0, t=50),
        # 启用缩放和平移
        dragmode='pan'
    )

    # 配置交互选项
    config = {
        'displayModeBar': True,
        'displaylogo': False,
        'modeBarButtonsToAdd': ['pan2d', 'zoom2d', 'zoomIn2d', 'zoomOut2d', 'autoScale2d', 'resetScale2d'],
        'scrollZoom': True
    }

    st.plotly_chart(fig, use_container_width=True, config=config)





def generate_smooth_trajectory(start_x, start_y, end_x, end_y, num_points=20):
    """生成平滑轨迹"""
    # 使用贝塞尔曲线生成平滑轨迹
    t = np.linspace(0, 1, num_points)

    # 控制点（可以添加一些随机性使轨迹更自然）
    mid_x = (start_x + end_x) / 2 + np.random.uniform(-5, 5)
    mid_y = (start_y + end_y) / 2 + np.random.uniform(-5, 5)

    # 二次贝塞尔曲线
    trajectory_x = (1-t)**2 * start_x + 2*(1-t)*t * mid_x + t**2 * end_x
    trajectory_y = (1-t)**2 * start_y + 2*(1-t)*t * mid_y + t**2 * end_y

    return trajectory_x, trajectory_y


def show_trajectory_animation():
    """显示轨迹动画"""
    st.info("🎬 动画功能演示：轨迹将逐步显示")

    # 这里可以添加更复杂的动画逻辑
    # 由于Streamlit的限制，我们使用进度条来模拟动画
    progress_bar = st.progress(0)
    status_text = st.empty()

    for i in range(100):
        progress_bar.progress(i + 1)
        status_text.text(f'动画进度: {i+1}%')
        # 在实际应用中，这里可以更新图表的数据

    status_text.text('✅ 动画播放完成！')
    st.success("轨迹动画播放完成")


def show_situation_info():
    """显示态势信息"""
    st.markdown("### 📊 态势信息")

    situation_data = st.session_state.situation_data

    # 基本信息
    st.markdown("#### 🎯 基本信息")
    col1, col2 = st.columns(2)

    with col1:
        st.metric("🗺️ 地图大小", f"{situation_data['map_size']}×{situation_data['map_size']} km")
        st.metric("🚁 无人机数量", f"{len(situation_data['drones'])}架")

    with col2:
        st.metric("📍 浮标数量", f"{len(situation_data['buoys'])}个")
        st.metric("⚡ 干扰类型", f"{len(situation_data['interference_config']['types'])}种")

    # 无人机详情
    with st.expander("🚁 无人机详情", expanded=False):
        for drone in situation_data['drones']:
            st.write(f"**{drone['id']}:** 位置({drone['x']:.1f}, {drone['y']:.1f}, {drone['z']}m) - {drone['status']}")

    # 浮标详情
    with st.expander("📍 浮标详情", expanded=False):
        for buoy in situation_data['buoys']:
            st.write(f"**{buoy['id']}:** 位置({buoy['x']:.1f}, {buoy['y']:.1f}) - 信号强度:{buoy['signal_strength']} - 检测范围:{buoy['detection_range']}km")

    # 干扰配置
    with st.expander("⚡ 干扰配置", expanded=False):
        config = situation_data['interference_config']
        st.write(f"**干扰类型:** {', '.join(config['types'])}")
        st.write(f"**干扰强度:** {config['strength']}")
        st.write(f"**影响范围:** {config['range']}km")


def show_planning_results():
    """显示AI规划结果（仅展示无人机规划和整体策略，取消浮标通信状态等表格）"""
    st.markdown("### 📋 AI规划结果")

    planning_result = st.session_state.planning_result

    if 'planning_result' in planning_result:
        # 显示整体策略
        if 'overall_strategy' in planning_result:
            st.info(f"🎯 **整体策略:** {planning_result['overall_strategy']}")

        # 显示每个无人机的规划
        for plan in planning_result['planning_result']:
            with st.expander(f"🚁 {plan['drone_id']} 规划详情", expanded=True):
                col1, col2 = st.columns(2)

                with col1:
                    st.write(f"**🎯 目标位置:**")
                    st.write(f"  • X: {plan['target_position']['x']:.1f} km")
                    st.write(f"  • Y: {plan['target_position']['y']:.1f} km")
                    st.write(f"  • Z: {plan['target_position']['z']} m")
                    st.write(f"**⚡ 干扰模式:** {plan['interference_mode']}")

                with col2:
                    priority = plan.get('priority', '未知')
                    priority_color = {"高": "🔴", "中": "🟡", "低": "🟢"}.get(priority, "⚪")
                    st.write(f"**📊 优先级:** {priority_color} {priority}")
                    st.write(f"**📈 预计效果:** {plan['expected_effect']}")

    elif 'raw_response' in planning_result:
        st.markdown("**🤖 AI原始回复:**")
        st.text_area("", value=planning_result['raw_response'], height=300, disabled=True)
        st.warning("⚠️ AI返回的格式不是标准JSON，请检查模型配置")

    else:
        st.error("❌ 规划结果格式异常")
