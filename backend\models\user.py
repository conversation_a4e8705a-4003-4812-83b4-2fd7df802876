from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List
from datetime import datetime
from enum import Enum


class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    VIEWER = "viewer"


class UserBase(SQLModel):
    """用户基础模型"""
    username: str = Field(index=True, unique=True, min_length=3, max_length=50)
    email: str = Field(index=True, unique=True)
    full_name: Optional[str] = Field(default=None, max_length=100)
    role: UserRole = Field(default=UserRole.USER)
    is_active: bool = Field(default=True)
    is_superuser: bool = Field(default=False)


class User(UserBase, table=True):
    """用户数据库模型"""
    id: Optional[int] = Field(default=None, primary_key=True)
    hashed_password: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    last_login: Optional[datetime] = Field(default=None)
    
    # 关联关系
    scenarios: List["Scenario"] = Relationship(back_populates="owner")
    tasks: List["Task"] = Relationship(back_populates="creator")
    model_configs: List["ModelConfig"] = Relationship(back_populates="owner")


class UserCreate(UserBase):
    """创建用户模型"""
    password: str = Field(min_length=6, max_length=100)


class UserUpdate(SQLModel):
    """更新用户模型"""
    username: Optional[str] = Field(default=None, min_length=3, max_length=50)
    email: Optional[str] = Field(default=None)
    full_name: Optional[str] = Field(default=None, max_length=100)
    role: Optional[UserRole] = Field(default=None)
    is_active: Optional[bool] = Field(default=None)
    password: Optional[str] = Field(default=None, min_length=6, max_length=100)


class UserRead(UserBase):
    """读取用户模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    last_login: Optional[datetime]


class UserLogin(SQLModel):
    """用户登录模型"""
    username: str
    password: str


class Token(SQLModel):
    """令牌模型"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserRead


class TokenData(SQLModel):
    """令牌数据模型"""
    username: Optional[str] = None
    user_id: Optional[int] = None


# 导入其他模型以避免循环导入
from backend.models.scenario import Scenario
from backend.models.task import Task
from backend.models.model_config import ModelConfig
