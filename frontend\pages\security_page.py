import streamlit as st
import sqlite3
import hashlib
import json
from pathlib import Path
from datetime import datetime, timedelta
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from frontend.utils.logger import log_operation, log_system


def init_security_database():
    """初始化安全数据库"""
    db_path = Path("data/security.db")
    db_path.parent.mkdir(exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建登录尝试记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS login_attempts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            ip_address TEXT,
            user_agent TEXT,
            success BOOLEAN,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建会话记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            session_token TEXT,
            ip_address TEXT,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
    ''')
    
    # 创建安全事件表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS security_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            event_type TEXT,
            severity TEXT,
            description TEXT,
            user_id INTEGER,
            ip_address TEXT,
            details TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()


def record_login_attempt(username, ip_address, user_agent, success):
    """记录登录尝试"""
    init_security_database()
    
    db_path = Path("data/security.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO login_attempts (username, ip_address, user_agent, success)
        VALUES (?, ?, ?, ?)
    ''', (username, ip_address, user_agent, success))
    
    conn.commit()
    conn.close()


def record_security_event(event_type, severity, description, user_id=None, ip_address=None, details=None):
    """记录安全事件"""
    init_security_database()
    
    db_path = Path("data/security.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO security_events (event_type, severity, description, user_id, ip_address, details)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (event_type, severity, description, user_id, ip_address, json.dumps(details) if details else None))
    
    conn.commit()
    conn.close()


def get_login_attempts(limit=100):
    """获取登录尝试记录"""
    db_path = Path("data/security.db")
    if not db_path.exists():
        return []
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT * FROM login_attempts 
        ORDER BY timestamp DESC 
        LIMIT ?
    ''', (limit,))
    
    attempts = cursor.fetchall()
    conn.close()
    
    return [
        {
            'id': attempt[0],
            'username': attempt[1],
            'ip_address': attempt[2],
            'user_agent': attempt[3],
            'success': attempt[4],
            'timestamp': attempt[5]
        }
        for attempt in attempts
    ]


def get_security_events(limit=100, severity=None):
    """获取安全事件"""
    db_path = Path("data/security.db")
    if not db_path.exists():
        return []
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    if severity:
        cursor.execute('''
            SELECT * FROM security_events 
            WHERE severity = ?
            ORDER BY timestamp DESC 
            LIMIT ?
        ''', (severity, limit))
    else:
        cursor.execute('''
            SELECT * FROM security_events 
            ORDER BY timestamp DESC 
            LIMIT ?
        ''', (limit,))
    
    events = cursor.fetchall()
    conn.close()
    
    return [
        {
            'id': event[0],
            'event_type': event[1],
            'severity': event[2],
            'description': event[3],
            'user_id': event[4],
            'ip_address': event[5],
            'details': json.loads(event[6]) if event[6] else None,
            'timestamp': event[7]
        }
        for event in events
    ]


def show():
    """显示安全管理页面"""
    if not st.session_state.get('authenticated', False):
        st.error("请先登录")
        return
    
    user = st.session_state.get('user', {})
    is_admin = user.get('role') == 'admin'
    
    if not is_admin:
        st.error("⚠️ 只有管理员可以访问安全管理功能")
        return
    
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
    ">
        <h2 style="margin: 0;">🔒 安全管理</h2>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">系统安全监控、审计和访问控制</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 安全功能选项卡
    tab1, tab2, tab3, tab4 = st.tabs(["🛡️ 安全概览", "🔍 登录审计", "⚠️ 安全事件", "🔐 访问控制"])
    
    with tab1:
        show_security_overview()
    
    with tab2:
        show_login_audit()
    
    with tab3:
        show_security_events()
    
    with tab4:
        show_access_control()


def show_security_overview():
    """显示安全概览"""
    st.markdown("### 🛡️ 安全状态概览")
    
    # 安全指标
    col1, col2, col3, col4 = st.columns(4)
    
    # 获取最近24小时的登录尝试
    login_attempts = get_login_attempts(1000)
    recent_attempts = [
        attempt for attempt in login_attempts
        if datetime.fromisoformat(attempt['timestamp']) > datetime.now() - timedelta(hours=24)
    ]
    
    failed_attempts = [attempt for attempt in recent_attempts if not attempt['success']]
    
    with col1:
        st.metric("24h登录尝试", len(recent_attempts))
    
    with col2:
        st.metric("失败登录", len(failed_attempts), 
                 delta=f"{'正常' if len(failed_attempts) < 10 else '需关注'}")
    
    with col3:
        # 获取安全事件
        security_events = get_security_events(100)
        high_severity_events = [event for event in security_events if event['severity'] == 'HIGH']
        st.metric("高危事件", len(high_severity_events),
                 delta=f"{'安全' if len(high_severity_events) == 0 else '需处理'}")
    
    with col4:
        # 活跃会话数（模拟）
        active_sessions = 1  # 当前用户
        st.metric("活跃会话", active_sessions)
    
    # 安全趋势图
    st.markdown("#### 📈 安全趋势")
    
    if login_attempts:
        import pandas as pd
        
        # 按小时统计登录尝试
        df = pd.DataFrame(login_attempts)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['hour'] = df['timestamp'].dt.floor('H')
        
        hourly_stats = df.groupby(['hour', 'success']).size().unstack(fill_value=0)
        
        if not hourly_stats.empty:
            st.line_chart(hourly_stats)
        else:
            st.info("📊 暂无足够数据生成趋势图")
    else:
        st.info("📊 暂无登录数据")
    
    # 安全建议
    st.markdown("#### 💡 安全建议")
    
    recommendations = []
    
    if len(failed_attempts) > 10:
        recommendations.append("⚠️ 检测到较多失败登录尝试，建议启用账户锁定机制")
    
    if len(high_severity_events) > 0:
        recommendations.append("🚨 存在高危安全事件，请立即处理")
    
    if not recommendations:
        recommendations.append("✅ 系统安全状态良好")
    
    for rec in recommendations:
        st.info(rec)


def show_login_audit():
    """显示登录审计"""
    st.markdown("### 🔍 登录审计")
    
    # 筛选选项
    col1, col2, col3 = st.columns(3)
    
    with col1:
        limit = st.selectbox("显示条数", [50, 100, 200, 500], index=1, key="login_audit_limit")

    with col2:
        show_failed_only = st.checkbox("仅显示失败登录", key="show_failed_only")

    with col3:
        if st.button("🔄 刷新数据", key="refresh_login_audit"):
            st.rerun()
    
    # 获取登录尝试数据
    login_attempts = get_login_attempts(limit)
    
    if show_failed_only:
        login_attempts = [attempt for attempt in login_attempts if not attempt['success']]
    
    if login_attempts:
        import pandas as pd
        
        # 转换为DataFrame
        df = pd.DataFrame(login_attempts)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 统计信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总尝试次数", len(df))
        
        with col2:
            success_count = len(df[df['success'] == True])
            st.metric("成功登录", success_count)
        
        with col3:
            failed_count = len(df[df['success'] == False])
            st.metric("失败登录", failed_count)
        
        with col4:
            unique_ips = df['ip_address'].nunique()
            st.metric("唯一IP", unique_ips)
        
        # 显示详细记录
        st.markdown("#### 📋 登录记录")
        
        # 格式化显示
        display_df = df[['timestamp', 'username', 'ip_address', 'success']].copy()
        display_df['timestamp'] = display_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
        display_df['success'] = display_df['success'].map({True: '✅ 成功', False: '❌ 失败'})
        display_df.columns = ['时间', '用户名', 'IP地址', '状态']
        
        # 添加状态颜色
        def color_status(val):
            # 确保val是字符串类型
            if not isinstance(val, str):
                val = str(val) if val is not None else ''

            if '成功' in val:
                return 'background-color: #d4edda; color: #155724'
            elif '失败' in val:
                return 'background-color: #f8d7da; color: #721c24'
            else:
                return ''
        
        styled_df = display_df.style.applymap(color_status, subset=['状态'])
        st.dataframe(styled_df, use_container_width=True, height=400)
        
        # 可疑活动检测
        st.markdown("#### 🚨 可疑活动检测")
        
        # 检测频繁失败的IP
        failed_df = df[df['success'] == False]
        if not failed_df.empty:
            ip_failures = failed_df['ip_address'].value_counts()
            suspicious_ips = ip_failures[ip_failures >= 5]
            
            if not suspicious_ips.empty:
                st.warning("⚠️ 检测到可疑IP地址（失败登录≥5次）:")
                for ip, count in suspicious_ips.items():
                    st.error(f"🚨 IP: {ip} - 失败次数: {count}")
            else:
                st.success("✅ 未检测到可疑IP活动")
        
    else:
        st.info("📝 暂无登录审计数据")


def show_security_events():
    """显示安全事件"""
    st.markdown("### ⚠️ 安全事件监控")
    
    # 筛选选项
    col1, col2, col3 = st.columns(3)
    
    with col1:
        limit = st.selectbox("显示条数", [50, 100, 200], index=1, key="security_events_limit")
    
    with col2:
        severity_filter = st.selectbox("严重程度", ["全部", "LOW", "MEDIUM", "HIGH", "CRITICAL"], key="severity_filter")
    
    with col3:
        if st.button("🔄 刷新事件", key="refresh_security_events"):
            st.rerun()
    
    # 获取安全事件
    severity = None if severity_filter == "全部" else severity_filter
    security_events = get_security_events(limit, severity)
    
    if security_events:
        import pandas as pd
        
        # 转换为DataFrame
        df = pd.DataFrame(security_events)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 统计信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总事件数", len(df))
        
        with col2:
            critical_count = len(df[df['severity'] == 'CRITICAL'])
            st.metric("严重事件", critical_count, delta=f"{'安全' if critical_count == 0 else '需处理'}")
        
        with col3:
            high_count = len(df[df['severity'] == 'HIGH'])
            st.metric("高危事件", high_count, delta=f"{'正常' if high_count == 0 else '需关注'}")
        
        with col4:
            recent_events = len(df[df['timestamp'] > datetime.now() - timedelta(hours=24)])
            st.metric("24h事件", recent_events)
        
        # 显示事件列表
        st.markdown("#### 📋 安全事件列表")
        
        # 格式化显示
        display_df = df[['timestamp', 'event_type', 'severity', 'description']].copy()
        display_df['timestamp'] = display_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
        display_df.columns = ['时间', '事件类型', '严重程度', '描述']
        
        # 添加严重程度颜色
        def color_severity(val):
            # 确保val是字符串类型
            if not isinstance(val, str):
                val = str(val) if val is not None else ''

            colors = {
                'LOW': 'background-color: #d1ecf1; color: #0c5460',
                'MEDIUM': 'background-color: #fff3cd; color: #856404',
                'HIGH': 'background-color: #f8d7da; color: #721c24',
                'CRITICAL': 'background-color: #721c24; color: white'
            }
            return colors.get(val, '')
        
        styled_df = display_df.style.applymap(color_severity, subset=['严重程度'])
        st.dataframe(styled_df, use_container_width=True, height=400)
        
    else:
        st.info("📝 暂无安全事件")
    
    # 手动记录安全事件
    st.markdown("#### ➕ 手动记录安全事件")
    
    with st.form("security_event_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            event_type = st.text_input("事件类型", placeholder="如：可疑登录、数据泄露等", key="event_type_input")
            severity = st.selectbox("严重程度", ["LOW", "MEDIUM", "HIGH", "CRITICAL"], key="event_severity")
        
        with col2:
            description = st.text_area("事件描述", placeholder="详细描述安全事件...")
        
        if st.form_submit_button("📝 记录事件", type="primary"):
            if event_type and description:
                # 获取当前用户信息
                current_user = st.session_state.get('user', {})
                record_security_event(
                    event_type=event_type,
                    severity=severity,
                    description=description,
                    user_id=current_user.get('id'),
                    details={"manual_entry": True}
                )
                log_operation("记录安全事件", "安全管理", {"event_type": event_type, "severity": severity})
                st.success("✅ 安全事件已记录")
                st.rerun()
            else:
                st.error("⚠️ 请填写事件类型和描述")


def show_access_control():
    """显示访问控制"""
    st.markdown("### 🔐 访问控制设置")
    
    # IP白名单/黑名单
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### ✅ IP白名单")

        whitelist_ip = st.text_input("添加白名单IP", placeholder="*************", key="whitelist_ip_input")
        if st.button("➕ 添加到白名单", key="add_to_whitelist_btn"):
            if whitelist_ip:
                st.success(f"✅ IP {whitelist_ip} 已添加到白名单")

        # 显示当前白名单
        st.markdown("**当前白名单：**")
        whitelist_ips = ["*************", "********"]  # 示例数据
        for ip in whitelist_ips:
            col_ip, col_remove = st.columns([3, 1])
            with col_ip:
                st.text(f"🟢 {ip}")
            with col_remove:
                if st.button("🗑️", key=f"remove_whitelist_{ip}", help=f"移除 {ip}"):
                    st.success(f"✅ 已移除白名单IP: {ip}")

    with col2:
        st.markdown("#### ❌ IP黑名单")

        blacklist_ip = st.text_input("添加黑名单IP", placeholder="*************", key="blacklist_ip_input")
        if st.button("➕ 添加到黑名单", key="add_to_blacklist_btn"):
            if blacklist_ip:
                st.success(f"✅ IP {blacklist_ip} 已添加到黑名单")

        # 显示当前黑名单
        st.markdown("**当前黑名单：**")
        blacklist_ips = ["*************", "**********"]  # 示例数据
        for ip in blacklist_ips:
            col_ip, col_remove = st.columns([3, 1])
            with col_ip:
                st.text(f"🔴 {ip}")
            with col_remove:
                if st.button("🗑️", key=f"remove_blacklist_{ip}", help=f"移除 {ip}"):
                    st.success(f"✅ 已移除黑名单IP: {ip}")
    
    # 用户权限管理
    st.markdown("#### 👥 用户权限管理")
    
    # 获取用户列表
    db_path = Path("data/users.db")
    if db_path.exists():
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, username, role FROM users")
        users = cursor.fetchall()
        conn.close()
        
        if users:
            import pandas as pd
            users_df = pd.DataFrame(users, columns=['ID', '用户名', '角色'])
            st.dataframe(users_df, use_container_width=True)
        else:
            st.info("📝 暂无用户数据")
    else:
        st.error("❌ 用户数据库不存在")
    
    # 会话管理
    st.markdown("#### 🔄 会话管理")
    
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🔄 刷新所有会话", use_container_width=True, key="refresh_sessions_btn"):
            st.success("✅ 所有用户会话已刷新")

    with col2:
        if st.button("🚪 强制下线所有用户", use_container_width=True, key="force_logout_all_btn"):
            st.warning("⚠️ 确认要强制下线所有用户吗？此操作不可撤销。")
            if st.button("确认强制下线", type="primary", key="confirm_force_logout"):
                st.success("✅ 所有用户已被强制下线")

    with col3:
        if st.button("🔒 锁定系统", use_container_width=True, key="lock_system_btn"):
            st.error("⚠️ 确认要锁定系统吗？锁定后只有超级管理员可以解锁。")
            if st.button("确认锁定", type="primary", key="confirm_lock_system"):
                st.success("🔒 系统已锁定")
