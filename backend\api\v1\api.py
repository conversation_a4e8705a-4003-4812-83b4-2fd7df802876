from fastapi import APIRouter

from backend.api.v1.endpoints import auth, users, scenarios, tasks, models, monitoring

api_router = APIRouter()

# 认证路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

# 用户管理路由
api_router.include_router(
    users.router,
    prefix="/users",
    tags=["users"]
)

# 场景管理路由
api_router.include_router(
    scenarios.router,
    prefix="/scenarios",
    tags=["scenarios"]
)

# 任务管理路由
api_router.include_router(
    tasks.router,
    prefix="/tasks",
    tags=["tasks"]
)

# 模型管理路由
api_router.include_router(
    models.router,
    prefix="/models",
    tags=["models"]
)

# 监控路由
api_router.include_router(
    monitoring.router,
    prefix="/monitoring",
    tags=["monitoring"]
)
