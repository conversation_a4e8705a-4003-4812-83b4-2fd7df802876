import torch
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor

from llm_integration.models.model_manager import model_manager
from backend.models.model_config import ModelConfig, ModelType
from backend.models.task import Task, DroneConfig, TrajectoryPlan, Waypoint, InterferenceMode
from backend.models.scenario import <PERSON><PERSON>rio, FloatBuoy

logger = logging.getLogger(__name__)


class InferenceEngine:
    """推理引擎"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.inference_cache = {}
        
    async def predict_trajectories(
        self,
        task: Task,
        scenario: Scenario,
        model_config: ModelConfig
    ) -> List[TrajectoryPlan]:
        """预测无人机轨迹"""
        try:
            # 确保模型已加载
            model_id = str(model_config.id)
            if not model_manager.is_model_loaded(model_id):
                if not model_manager.load_model(model_config):
                    raise Exception(f"Failed to load model {model_config.name}")
            
            # 构建输入提示
            prompt = self._build_trajectory_prompt(task, scenario)
            
            # 执行推理
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._generate_trajectories,
                model_id,
                prompt,
                task,
                scenario
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to predict trajectories: {e}")
            return []
    
    def _build_trajectory_prompt(self, task: Task, scenario: Scenario) -> str:
        """构建轨迹预测提示"""
        # 场景信息
        scenario_info = {
            "area_bounds": scenario.area_bounds,
            "environment": scenario.environment.dict(),
            "float_buoys": [buoy.dict() for buoy in scenario.float_buoys]
        }
        
        # 任务信息
        task_info = {
            "objective_weights": task.objective_weights.dict(),
            "target_buoys": task.target_buoys,
            "available_drones": [drone.dict() for drone in task.available_drones],
            "constraints": task.constraints
        }
        
        prompt = f"""
任务：为多架无人机规划最优轨迹以执行干扰任务

场景信息：
{json.dumps(scenario_info, indent=2, ensure_ascii=False)}

任务要求：
{json.dumps(task_info, indent=2, ensure_ascii=False)}

请为每架无人机生成详细的轨迹规划，包括：
1. 航点序列（经纬度、高度、时间戳）
2. 在每个航点的具体行动
3. 干扰模式和参数设置
4. 预计能耗和执行时间
5. 风险评估

输出格式为JSON，包含每架无人机的完整轨迹规划。
"""
        return prompt
    
    def _generate_trajectories(
        self,
        model_id: str,
        prompt: str,
        task: Task,
        scenario: Scenario
    ) -> List[TrajectoryPlan]:
        """生成轨迹规划"""
        try:
            # 获取模型管道
            pipeline = model_manager.pipelines.get(model_id)
            if not pipeline:
                raise Exception(f"Pipeline not found for model {model_id}")
            
            # 执行推理
            config = model_manager.model_configs[model_id]
            
            if config.model_type in [ModelType.GPT, ModelType.LLAMA]:
                # 生成式模型
                outputs = pipeline(
                    prompt,
                    max_length=config.max_tokens,
                    temperature=config.temperature,
                    top_p=config.top_p,
                    top_k=config.top_k,
                    do_sample=True,
                    num_return_sequences=1
                )
                generated_text = outputs[0]['generated_text']
                
                # 解析生成的文本
                trajectories = self._parse_generated_trajectories(
                    generated_text, task, scenario
                )
                
            elif config.model_type == ModelType.T5:
                # 文本到文本生成
                outputs = pipeline(
                    prompt,
                    max_length=config.max_tokens,
                    temperature=config.temperature,
                    top_p=config.top_p,
                    top_k=config.top_k,
                    do_sample=True
                )
                generated_text = outputs[0]['generated_text']
                
                trajectories = self._parse_generated_trajectories(
                    generated_text, task, scenario
                )
                
            else:
                # 使用启发式算法作为后备
                trajectories = self._generate_heuristic_trajectories(task, scenario)
            
            return trajectories
            
        except Exception as e:
            logger.error(f"Failed to generate trajectories: {e}")
            # 返回启发式算法结果作为后备
            return self._generate_heuristic_trajectories(task, scenario)
    
    def _parse_generated_trajectories(
        self,
        generated_text: str,
        task: Task,
        scenario: Scenario
    ) -> List[TrajectoryPlan]:
        """解析生成的轨迹文本"""
        try:
            # 尝试从生成的文本中提取JSON
            import re
            json_match = re.search(r'\{.*\}', generated_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                trajectory_data = json.loads(json_str)
                
                trajectories = []
                for drone_id, drone_data in trajectory_data.items():
                    if isinstance(drone_data, dict) and 'waypoints' in drone_data:
                        waypoints = []
                        for wp_data in drone_data['waypoints']:
                            waypoint = Waypoint(
                                latitude=wp_data.get('latitude', 0.0),
                                longitude=wp_data.get('longitude', 0.0),
                                altitude=wp_data.get('altitude', 100.0),
                                timestamp=datetime.fromisoformat(wp_data['timestamp']) if 'timestamp' in wp_data else None,
                                action=wp_data.get('action'),
                                parameters=wp_data.get('parameters', {})
                            )
                            waypoints.append(waypoint)
                        
                        trajectory = TrajectoryPlan(
                            drone_id=drone_id,
                            waypoints=waypoints,
                            estimated_duration=drone_data.get('estimated_duration', 3600.0),
                            estimated_energy_consumption=drone_data.get('estimated_energy_consumption', 50.0),
                            interference_schedule=drone_data.get('interference_schedule', []),
                            risk_assessment=drone_data.get('risk_assessment', {})
                        )
                        trajectories.append(trajectory)
                
                return trajectories
            
        except Exception as e:
            logger.error(f"Failed to parse generated trajectories: {e}")
        
        # 解析失败，使用启发式算法
        return self._generate_heuristic_trajectories(task, scenario)
    
    def _generate_heuristic_trajectories(
        self,
        task: Task,
        scenario: Scenario
    ) -> List[TrajectoryPlan]:
        """使用启发式算法生成轨迹"""
        trajectories = []
        
        try:
            # 获取目标浮漂
            target_buoys = [
                buoy for buoy in scenario.float_buoys
                if buoy.id in task.target_buoys
            ]
            
            if not target_buoys:
                return trajectories
            
            # 为每架无人机分配目标
            drone_assignments = self._assign_drones_to_targets(
                task.available_drones, target_buoys
            )
            
            for drone, assigned_buoys in drone_assignments.items():
                waypoints = []
                
                # 起始点（假设在场景边界内的随机位置）
                bounds = scenario.area_bounds
                start_lat = bounds.get('min_lat', 0) + 0.1
                start_lon = bounds.get('min_lon', 0) + 0.1
                
                # 添加起始航点
                waypoints.append(Waypoint(
                    latitude=start_lat,
                    longitude=start_lon,
                    altitude=100.0,
                    timestamp=datetime.utcnow(),
                    action="takeoff",
                    parameters={"altitude": 100.0}
                ))
                
                # 为每个目标浮漂创建航点
                for i, buoy in enumerate(assigned_buoys):
                    # 接近目标
                    approach_waypoint = Waypoint(
                        latitude=buoy.latitude + 0.001,  # 稍微偏移
                        longitude=buoy.longitude + 0.001,
                        altitude=150.0,
                        timestamp=datetime.utcnow(),
                        action="approach_target",
                        parameters={"target_id": buoy.id}
                    )
                    waypoints.append(approach_waypoint)
                    
                    # 执行干扰
                    interference_waypoint = Waypoint(
                        latitude=buoy.latitude,
                        longitude=buoy.longitude,
                        altitude=120.0,
                        timestamp=datetime.utcnow(),
                        action="execute_interference",
                        parameters={
                            "target_id": buoy.id,
                            "interference_mode": "jamming",
                            "power_level": 80.0,
                            "duration": 300  # 5分钟
                        }
                    )
                    waypoints.append(interference_waypoint)
                
                # 返回基地
                return_waypoint = Waypoint(
                    latitude=start_lat,
                    longitude=start_lon,
                    altitude=50.0,
                    timestamp=datetime.utcnow(),
                    action="return_to_base",
                    parameters={}
                )
                waypoints.append(return_waypoint)
                
                # 创建轨迹规划
                trajectory = TrajectoryPlan(
                    drone_id=drone.id,
                    waypoints=waypoints,
                    estimated_duration=len(waypoints) * 300.0,  # 每个航点5分钟
                    estimated_energy_consumption=len(waypoints) * 10.0,  # 每个航点10%电量
                    interference_schedule=[
                        {
                            "target_id": buoy.id,
                            "start_time": datetime.utcnow().isoformat(),
                            "duration": 300,
                            "mode": "jamming"
                        }
                        for buoy in assigned_buoys
                    ],
                    risk_assessment={
                        "collision_risk": 0.1,
                        "detection_risk": 0.3,
                        "weather_risk": 0.2,
                        "overall_risk": 0.25
                    }
                )
                
                trajectories.append(trajectory)
        
        except Exception as e:
            logger.error(f"Failed to generate heuristic trajectories: {e}")
        
        return trajectories
    
    def _assign_drones_to_targets(
        self,
        drones: List[DroneConfig],
        targets: List[FloatBuoy]
    ) -> Dict[DroneConfig, List[FloatBuoy]]:
        """将无人机分配给目标"""
        assignments = {}
        
        if not drones or not targets:
            return assignments
        
        # 简单的轮询分配策略
        for i, drone in enumerate(drones):
            assigned_targets = []
            for j, target in enumerate(targets):
                if j % len(drones) == i:
                    assigned_targets.append(target)
            assignments[drone] = assigned_targets
        
        return assignments
    
    async def analyze_performance(
        self,
        task: Task,
        execution_results: Dict[str, Any]
    ) -> Dict[str, float]:
        """分析任务执行性能"""
        try:
            metrics = {}
            
            # 干扰成功率
            total_targets = len(task.target_buoys)
            successful_interferences = execution_results.get('successful_interferences', 0)
            metrics['interference_success_rate'] = successful_interferences / total_targets if total_targets > 0 else 0.0
            
            # 能耗效率
            total_energy_used = execution_results.get('total_energy_used', 0)
            total_energy_available = sum(drone.battery_capacity for drone in task.available_drones)
            metrics['energy_efficiency'] = 1.0 - (total_energy_used / total_energy_available) if total_energy_available > 0 else 0.0
            
            # 完成时间效率
            actual_duration = execution_results.get('actual_duration', 0)
            estimated_duration = execution_results.get('estimated_duration', 1)
            metrics['time_efficiency'] = estimated_duration / actual_duration if actual_duration > 0 else 0.0
            
            # 法规合规性
            violations = execution_results.get('regulatory_violations', 0)
            metrics['regulatory_compliance'] = max(0.0, 1.0 - violations * 0.1)
            
            # 综合评分
            weights = task.objective_weights
            metrics['overall_score'] = (
                metrics['interference_success_rate'] * weights.interference_success_rate +
                metrics['energy_efficiency'] * weights.energy_efficiency +
                metrics['time_efficiency'] * weights.completion_time +
                metrics['regulatory_compliance'] * weights.regulatory_compliance
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to analyze performance: {e}")
            return {}


# 全局推理引擎实例
inference_engine = InferenceEngine()
